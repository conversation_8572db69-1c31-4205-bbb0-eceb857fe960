#!/bin/bash

# GameFlex Backend - Production Deployment Script
# This script deploys the Terraform infrastructure to the production environment

set -e  # Exit on any error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print functions
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🚀 Starting GameFlex Backend Production Deployment..."

# Set environment variables
export ENVIRONMENT=production
export PROJECT_NAME=gameflex

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Check if we're in the backend directory
if [ ! -f "$SCRIPT_DIR/terraform/main.tf" ]; then
    print_error "This script must be run from the backend directory"
    echo "   Current directory: $SCRIPT_DIR"
    echo "   Expected to find: terraform/main.tf"
    exit 1
fi

# Check if production tfvars exists
if [ ! -f "$SCRIPT_DIR/terraform/environments/production.tfvars" ]; then
    print_error "production.tfvars not found"
    echo "💡 Update terraform/environments/production.tfvars with proper production configuration"
    exit 1
fi

print_status "Using production configuration from terraform/environments/production.tfvars..."

# Production safety check
print_warning "⚠️  PRODUCTION DEPLOYMENT ⚠️"
echo "This will deploy to the PRODUCTION environment!"
echo "Make sure you have:"
echo "  - Reviewed all changes"
echo "  - Tested in staging environment"
echo "  - Backed up any critical data"
echo ""
read -p "Are you sure you want to deploy to production? Type 'yes' to confirm: " -r
if [[ ! $REPLY == "yes" ]]; then
    echo "Production deployment cancelled."
    exit 1
fi

# Check if AWS credentials are configured
print_status "Checking AWS credentials..."
if ! aws sts get-caller-identity > /dev/null 2>&1; then
    print_error "AWS credentials not configured or invalid"
    echo "   Please run 'aws configure' or set AWS environment variables"
    exit 1
fi

print_success "Prerequisites check passed"

# Check if this is first time setup
FIRST_TIME_SETUP=false
if [ ! -d "$SCRIPT_DIR/terraform/.terraform" ]; then
    FIRST_TIME_SETUP=true
    print_warning "First time setup detected"
    echo "   This will initialize Terraform backend and create state bucket if needed"
fi

# Run the main deployment script
print_status "Running Terraform deployment for production..."

if [ "$FIRST_TIME_SETUP" = true ]; then
    # First time setup - initialize backend
    "$SCRIPT_DIR/terraform/scripts/setup-backend.sh" production
    "$SCRIPT_DIR/terraform/scripts/deploy.sh" production --init
else
    # Regular deployment
    "$SCRIPT_DIR/terraform/scripts/deploy.sh" production
fi

if [ $? -ne 0 ]; then
    print_error "Terraform deployment failed"
    exit 1
fi

print_success "Terraform deployment completed successfully!"

# Get the outputs
print_status "Deployment Information:"
echo "=========================="

cd "$SCRIPT_DIR/terraform"

# Get Terraform outputs
API_URL=$(terraform output -raw api_gateway_url 2>/dev/null || echo "Not available")
USER_POOL_ID=$(terraform output -raw user_pool_id 2>/dev/null || echo "Not available")
USER_POOL_CLIENT_ID=$(terraform output -raw user_pool_client_id 2>/dev/null || echo "Not available")
R2_SECRET_NAME=$(terraform output -raw r2_secret_name 2>/dev/null || echo "Not available")
CUSTOM_DOMAIN=$(terraform output -raw custom_domain_name 2>/dev/null || echo "Not available")

echo "API Gateway URL: $API_URL"
echo "User Pool ID: $USER_POOL_ID"
echo "User Pool Client ID: $USER_POOL_CLIENT_ID"
echo "R2 Secret Name: $R2_SECRET_NAME"
echo "Custom Domain: $CUSTOM_DOMAIN"
echo ""

# Instructions for next steps
echo "🔧 Next Steps:"
echo "=============="
echo "1. Configure your DNS to point api.gameflex.io to the API Gateway"
echo "2. Configure your DNS to point media.gameflex.io to your CloudFlare R2 bucket"
echo "3. Update the R2 secret in AWS Secrets Manager with your CloudFlare R2 credentials"
echo "4. Seed the database (if needed):"
echo "   ./seed.sh production"
echo ""
echo "5. Test the deployment:"
echo "   curl $API_URL/health"
echo ""
echo "6. Monitor the deployment and set up alerts"
echo ""
echo "🎉 Production deployment complete!"

# GameFlex Backend Deployment Guide

This document explains how to deploy the GameFlex backend to different environments using Terraform.

## Environment-Specific Configuration

We use separate Terraform variable files for each environment:

- `terraform/environments/development.tfvars` - Development environment
- `terraform/environments/staging.tfvars` - Staging environment
- `terraform/environments/production.tfvars` - Production environment

## Prerequisites

1. **Terraform**: Install Terraform >= 1.0
2. **AWS CLI**: Configure AWS credentials
3. **Node.js**: For Lambda function packaging

## Deployment Commands

### Development Environment

```bash
# Deploy development infrastructure
./deploy.sh

# Seed with test data
./seed.sh

# Start local development server (optional)
./start.sh
```

### Staging Environment

```bash
# Deploy staging infrastructure
./deploy-staging.sh

# Seed staging data
./seed.sh staging
```

### Production Environment

```bash
# Deploy production infrastructure (with safety prompts)
./deploy-production.sh

# Seed production data (if needed)
./seed.sh production
## First Time Setup

For the first deployment to any environment, you need to set up the Terraform backend:

```bash
# Setup backend for development
./terraform/scripts/setup-backend.sh development

# Setup backend for staging
./terraform/scripts/setup-backend.sh staging

# Setup backend for production
./terraform/scripts/setup-backend.sh production
```

## Resource Naming

Each environment creates resources with environment-specific names:

### Development
- Tables: `gameflex-development-*` (e.g., `gameflex-development-Users`)
- Secrets: `gameflex-development-r2-config`, `gameflex-development-app-config`
- Functions: `gameflex-development-*` (e.g., `gameflex-development-auth`)
- Domain: Uses API Gateway URL (no custom domain)

### Staging
- Tables: `gameflex-staging-*` (e.g., `gameflex-staging-Users`)
- Secrets: `gameflex-staging-r2-config`, `gameflex-staging-app-config`
- Functions: `gameflex-staging-*` (e.g., `gameflex-staging-auth`)
- Domain: `staging.api.gameflex.io`

### Production
- Tables: `gameflex-production-*` (e.g., `gameflex-production-Users`)
- Secrets: `gameflex-production-r2-config`, `gameflex-production-app-config`
- Functions: `gameflex-production-*` (e.g., `gameflex-production-auth`)
- Domain: `api.gameflex.io`

## Configuration Management

### Environment Variables

Update the `.tfvars` files in `terraform/environments/` with your configuration:

```hcl
# CloudFlare R2 Configuration
r2_account_id        = "your_r2_account_id"
r2_access_key_id     = "your_r2_access_key"
r2_secret_access_key = "your_r2_secret_key"
r2_endpoint          = "https://your_account_id.r2.cloudflarestorage.com"
r2_bucket_name       = "gameflex-environment"
r2_public_url        = "https://your_r2_public_url"

# Custom Domain (staging/production)
domain_name     = "staging.api.gameflex.io"
certificate_arn = "arn:aws:acm:us-east-1:account:certificate/cert-id"
```

### Secrets Management

After deployment, update secrets in AWS Secrets Manager:

```bash
# Update R2 configuration
aws secretsmanager put-secret-value \
  --secret-id gameflex-development-r2-config \
  --secret-string '{"accountId":"...","accessKeyId":"...","secretAccessKey":"...","endpoint":"...","bucketName":"...","publicUrl":"..."}'
```

## Benefits of Terraform Approach

1. **Infrastructure as Code**: Version-controlled infrastructure
2. **State Management**: Proper state tracking and drift detection
3. **Environment Isolation**: Separate state files for each environment
4. **Resource Dependencies**: Automatic dependency resolution
5. **Plan Before Apply**: See changes before they're made

## Troubleshooting

### Common Issues

1. **Backend Not Initialized**
   ```bash
   ./terraform/scripts/setup-backend.sh <environment>
   ```

2. **State Lock Issues**
   ```bash
   cd terraform
   terraform force-unlock <lock-id>
   ```

3. **Resource Already Exists**
   - Import existing resources into Terraform state
   - Or destroy and recreate (development only)

### Certificate Configuration

For staging and production deployments:
- **Staging**: Configure certificate for `staging.api.gameflex.io`
- **Production**: Configure certificate for `api.gameflex.io`

Update the `certificate_arn` in the appropriate `.tfvars` file before deploying.

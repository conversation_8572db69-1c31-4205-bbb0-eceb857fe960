# GameFlex Backend Configuration

This document explains how to configure the GameFlex backend using Terraform.

## Overview

The GameFlex backend uses a comprehensive configuration approach:

- **Terraform Variables**: Environment-specific configuration in `.tfvars` files
- **AWS Secrets Manager**: Stores sensitive credentials (R2, API tokens) for all environments
- **Environment Isolation**: Separate Terraform state and configuration for each environment
- **Type Safety**: Variable validation and type checking

This provides:

- **Security**: Sensitive credentials stored in AWS Secrets Manager
- **Infrastructure as Code**: All infrastructure defined in version-controlled Terraform
- **Environment Isolation**: Clear separation between dev/staging/production
- **State Management**: Proper state tracking and drift detection
- **Version Control**: Configuration templates are committed, secrets are not

## Configuration Structure

```
terraform/
├── environments/           # Environment-specific configurations
│   ├── development.tfvars # Development environment variables
│   ├── staging.tfvars     # Staging environment variables
│   └── production.tfvars  # Production environment variables
├── backend-configs/       # Terraform backend configurations
│   ├── development.hcl    # Development state backend
│   ├── staging.hcl        # Staging state backend
│   └── production.hcl     # Production state backend
└── variables.tf           # Variable definitions with validation
```

## Initial Setup

### 1. Configure Environment Variables

Update the appropriate `.tfvars` file in `terraform/environments/`:

```hcl
# terraform/environments/development.tfvars
environment  = "development"
project_name = "gameflex"
aws_region   = "us-west-2"

# CloudFlare R2 Configuration
r2_account_id        = "your_r2_account_id"
r2_access_key_id     = "your_r2_access_key"
r2_secret_access_key = "your_r2_secret_key"
r2_endpoint          = "https://your_account_id.r2.cloudflarestorage.com"
r2_bucket_name       = "gameflex-development"
r2_public_url        = "https://pub-34709f09e8384ef1a67928492571c01d.r2.dev"
```

### 2. Configure Environment Files

Create `.env` and `.env.staging` files with your actual credentials:

#### Development (.env)
```bash
# CloudFlare R2 Configuration - DEVELOPMENT
R2_ACCOUNT_ID=your_r2_account_id
R2_ACCESS_KEY_ID=your_r2_access_key_id
R2_SECRET_ACCESS_KEY=your_r2_secret_access_key
R2_ENDPOINT=https://your_account_id.r2.cloudflarestorage.com
R2_BUCKET_NAME=gameflex-development
R2_PUBLIC_URL=https://pub-your_subdomain.r2.dev

# Application Configuration
CLOUDFLARE_API_TOKEN=your_cloudflare_api_token
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=DevTest123!
```

#### Staging (.env.staging)
```bash
# CloudFlare R2 Configuration - STAGING
R2_ACCOUNT_ID=your_r2_account_id
R2_ACCESS_KEY_ID=your_r2_access_key_id
R2_SECRET_ACCESS_KEY=your_r2_secret_access_key
R2_ENDPOINT=https://your_account_id.r2.cloudflarestorage.com
R2_BUCKET_NAME=gameflex-staging
R2_PUBLIC_URL=https://staging.media.gameflex.io

# Application Configuration
CLOUDFLARE_API_TOKEN=your_cloudflare_api_token
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=StagingTest123!
```

### 3. Deploy and Seed

The deployment and seeding process will automatically:
- Deploy AWS resources including Secrets Manager secrets
- Upload credentials from `.env` files to AWS Secrets Manager
- Configure Lambda functions to use Secrets Manager

## Configuration Structure

### Development Environment
```toml
[default.deploy.parameters]
stack_name = "gameflex-development"
# ... deployment parameters

[default.global.parameters]
environment_variables = """
ENVIRONMENT=development
# ... all development environment variables
"""
```

### Staging Environment
```toml
[staging.deploy.parameters]
stack_name = "gameflex-staging"
# ... staging deployment parameters

[staging.global.parameters]
environment_variables = """
ENVIRONMENT=staging
# ... all staging environment variables
"""
```

### Production Environment
```toml
[production.deploy.parameters]
stack_name = "gameflex-production"
# ... production deployment parameters

[production.global.parameters]
environment_variables = """
ENVIRONMENT=production
# ... all production environment variables
"""
```

## Environment Management

### View Configuration
```bash
# List all environments
./scripts/env-manager.sh list

# Show specific environment
./scripts/env-manager.sh show staging

# Test loading environment variables
./scripts/env-manager.sh test-load staging
```

### Validate Configuration
```bash
# Validate environment configuration
./scripts/env-manager.sh validate staging
```

### Update from Deployment
```bash
# Update configuration with CloudFormation outputs
./scripts/env-manager.sh update-from-stack staging
```

## Deployment and Seeding

### Development
```bash
# 1. Deploy infrastructure
sam build
sam deploy

# 2. Seed data and populate secrets
./scripts/seed-data.sh
# This will:
# - Upload R2 and app config to AWS Secrets Manager
# - Seed DynamoDB with test data
# - Upload test images to R2
```

### Staging
```bash
# 1. Deploy infrastructure
export CERTIFICATE_ARN="your-cert-arn"
./deploy-staging.sh

# 2. Seed staging data
./seed-staging.sh
# This will:
# - Upload staging credentials to AWS Secrets Manager
# - Seed staging DynamoDB with test data
# - Upload test images to staging R2 bucket
```

### Production
```bash
# 1. Deploy infrastructure
export CERTIFICATE_ARN="your-cert-arn"
./deploy-production.sh

# 2. Seed production data (manual)
./scripts/seed-data.sh -e production
```

## Secrets Manager Integration

### How It Works

1. **Environment Files**: Store actual credentials in `.env` and `.env.staging`
2. **Deployment**: SAM creates empty Secrets Manager secrets
3. **Seeding**: Seed scripts upload credentials from `.env` files to Secrets Manager
4. **Runtime**: Lambda functions retrieve credentials from Secrets Manager

### Secrets Structure

#### R2 Configuration Secret
```json
{
  "accountId": "your_r2_account_id",
  "accessKeyId": "your_r2_access_key_id",
  "secretAccessKey": "your_r2_secret_access_key",
  "endpoint": "https://your_account_id.r2.cloudflarestorage.com",
  "bucketName": "gameflex-development",
  "publicUrl": "https://pub-your_subdomain.r2.dev"
}
```

#### App Configuration Secret
```json
{
  "cloudflareApiToken": "your_cloudflare_api_token",
  "testUserEmail": "<EMAIL>",
  "testUserPassword": "DevTest123!",
  "debugMode": "development",
  "apiBaseUrl": "https://api-url",
  "userPoolId": "us-west-2_abc123",
  "userPoolClientId": "client123"
}
```

## Security Considerations

### What to Commit
- ✅ `samconfig.toml.example` - Template with non-sensitive configuration
- ✅ `.env.example` - Template for development secrets
- ✅ `.env.staging.example` - Template for staging secrets
- ❌ `samconfig.toml` - Contains deployment configuration (gitignored)
- ❌ `.env` - Contains actual development secrets (gitignored)
- ❌ `.env.staging` - Contains actual staging secrets (gitignored)

### Credential Management
- **Development**: Direct credentials in `samconfig.toml`
- **Staging/Production**: Credentials stored in AWS Secrets Manager
- **Never commit**: Real API keys, access keys, or secrets

### Environment Variables Priority
1. **samconfig.toml** - Primary source
2. **Environment variables** - Override if set
3. **Legacy .env files** - Fallback only

## Troubleshooting

### Configuration Not Found
```bash
❌ samconfig.toml not found
💡 Initialize it from the example:
   ./scripts/env-manager.sh init
```

### Invalid Configuration
```bash
# Test loading configuration
./scripts/env-manager.sh test-load development

# Show configuration
./scripts/env-manager.sh show development
```

### Missing Credentials
```bash
# Check what placeholders need to be replaced
grep -n "YOUR_" samconfig.toml
```

### Deployment Failures
```bash
# Verify staging configuration
./scripts/env-manager.sh validate staging

# Check parameter overrides
sam deploy --config-env staging --parameter-overrides "CertificateArn=test" --no-execute-changeset
```

## Migration from .env Files

If you have existing `.env` files, you can migrate manually:

1. **Initialize**: `./scripts/env-manager.sh init`
2. **Copy values**: From `.env` files to appropriate sections in `samconfig.toml`
3. **Test**: `./scripts/env-manager.sh test-load development`
4. **Deploy**: Verify deployment works with new configuration
5. **Cleanup**: Remove old `.env` files

## Best Practices

1. **Keep secrets out of version control**: Only commit `samconfig.toml.example`
2. **Use descriptive placeholders**: Make it clear what needs to be replaced
3. **Validate before deployment**: Always test configuration loading
4. **Backup before updates**: Scripts automatically create backups
5. **Use environment-specific values**: Don't share credentials between environments

## Example Workflow

```bash
# 1. Initial setup
git clone your-repo
cd backend
./scripts/env-manager.sh init

# 2. Configure credentials
nano samconfig.toml  # Replace YOUR_* placeholders

# 3. Test configuration
./scripts/env-manager.sh test-load development

# 4. Deploy
sam build
sam deploy

# 5. Deploy staging
export CERTIFICATE_ARN="your-cert"
./deploy-staging.sh
```

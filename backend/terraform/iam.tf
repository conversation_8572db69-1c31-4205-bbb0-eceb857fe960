# GameFlex Backend - IAM Roles and Policies
# IAM configuration for Lambda functions

# Lambda execution role
resource "aws_iam_role" "lambda_execution_role" {
  name = "${local.name_prefix}-lambda-execution-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })

  tags = local.common_tags
}

# Basic Lambda execution policy
resource "aws_iam_role_policy_attachment" "lambda_basic_execution" {
  role       = aws_iam_role.lambda_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

# DynamoDB access policy
resource "aws_iam_policy" "dynamodb_access" {
  name        = "${local.name_prefix}-dynamodb-access"
  description = "DynamoDB access policy for Lambda functions"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "dynamodb:BatchGetItem",
          "dynamodb:BatchWriteItem",
          "dynamodb:DeleteItem",
          "dynamodb:GetItem",
          "dynamodb:PutItem",
          "dynamodb:Query",
          "dynamodb:Scan",
          "dynamodb:UpdateItem"
        ]
        Resource = [
          aws_dynamodb_table.users.arn,
          aws_dynamodb_table.posts.arn,
          aws_dynamodb_table.media.arn,
          aws_dynamodb_table.user_profiles.arn,
          aws_dynamodb_table.comments.arn,
          aws_dynamodb_table.likes.arn,
          aws_dynamodb_table.follows.arn,
          aws_dynamodb_table.channels.arn,
          aws_dynamodb_table.channel_members.arn,
          aws_dynamodb_table.reflexes.arn,
          "${aws_dynamodb_table.users.arn}/index/*",
          "${aws_dynamodb_table.comments.arn}/index/*",
          "${aws_dynamodb_table.likes.arn}/index/*",
          "${aws_dynamodb_table.channels.arn}/index/*",
          "${aws_dynamodb_table.channel_members.arn}/index/*",
          "${aws_dynamodb_table.reflexes.arn}/index/*"
        ]
      }
    ]
  })

  tags = local.common_tags
}

# Attach DynamoDB policy to Lambda role
resource "aws_iam_role_policy_attachment" "lambda_dynamodb_access" {
  role       = aws_iam_role.lambda_execution_role.name
  policy_arn = aws_iam_policy.dynamodb_access.arn
}

# Cognito access policy
resource "aws_iam_policy" "cognito_access" {
  name        = "${local.name_prefix}-cognito-access"
  description = "Cognito access policy for Lambda functions"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "cognito-idp:*"
        ]
        Resource = aws_cognito_user_pool.main.arn
      },
      {
        Effect = "Allow"
        Action = [
          "cognito-idp:GetUser"
        ]
        Resource = aws_cognito_user_pool.main.arn
      }
    ]
  })

  tags = local.common_tags
}

# Attach Cognito policy to Lambda role
resource "aws_iam_role_policy_attachment" "lambda_cognito_access" {
  role       = aws_iam_role.lambda_execution_role.name
  policy_arn = aws_iam_policy.cognito_access.arn
}

# Secrets Manager access policy
resource "aws_iam_policy" "secrets_manager_access" {
  name        = "${local.name_prefix}-secrets-manager-access"
  description = "Secrets Manager access policy for Lambda functions"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "secretsmanager:GetSecretValue"
        ]
        Resource = [
          aws_secretsmanager_secret.r2_config.arn,
          aws_secretsmanager_secret.app_config.arn
        ]
        Condition = {
          StringEquals = {
            "aws:RequestedRegion" = var.aws_region
          }
        }
      }
    ]
  })

  tags = local.common_tags
}

# Attach Secrets Manager policy to Lambda role
resource "aws_iam_role_policy_attachment" "lambda_secrets_manager_access" {
  role       = aws_iam_role.lambda_execution_role.name
  policy_arn = aws_iam_policy.secrets_manager_access.arn
}

# API Gateway execution role for Lambda authorizer
resource "aws_iam_role" "api_gateway_execution_role" {
  name = "${local.name_prefix}-api-gateway-execution-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "apigateway.amazonaws.com"
        }
      }
    ]
  })

  tags = local.common_tags
}

# API Gateway Lambda invoke policy
resource "aws_iam_policy" "api_gateway_lambda_invoke" {
  name        = "${local.name_prefix}-api-gateway-lambda-invoke"
  description = "API Gateway Lambda invoke policy"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "lambda:InvokeFunction"
        ]
        Resource = "arn:aws:lambda:${var.aws_region}:${data.aws_caller_identity.current.account_id}:function:${local.function_names.authorizer}"
      }
    ]
  })

  tags = local.common_tags
}

# Attach Lambda invoke policy to API Gateway role
resource "aws_iam_role_policy_attachment" "api_gateway_lambda_invoke" {
  role       = aws_iam_role.api_gateway_execution_role.name
  policy_arn = aws_iam_policy.api_gateway_lambda_invoke.arn
}

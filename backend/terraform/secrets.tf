# GameFlex Backend - AWS Secrets Manager
# Configuration for storing sensitive application data

# R2 Configuration Secret
resource "aws_secretsmanager_secret" "r2_config" {
  name        = local.secret_names.r2_config
  description = "CloudFlare R2 configuration for GameFlex"

  # Retention policy for production/staging
  recovery_window_in_days = local.is_prod_or_staging ? 30 : 0

  tags = local.common_tags
}

# R2 Configuration Secret Version
resource "aws_secretsmanager_secret_version" "r2_config" {
  secret_id = aws_secretsmanager_secret.r2_config.id
  secret_string = jsonencode({
    accountId       = var.r2_account_id
    accessKeyId     = var.r2_access_key_id
    secretAccessKey = var.r2_secret_access_key
    endpoint        = var.r2_endpoint
    bucketName      = var.r2_bucket_name
    publicUrl       = var.r2_public_url
  })

  lifecycle {
    ignore_changes = [secret_string]
  }
}

# Application Configuration Secret
resource "aws_secretsmanager_secret" "app_config" {
  name        = local.secret_names.app_config
  description = "General application configuration for GameFlex"

  # Retention policy for production/staging
  recovery_window_in_days = local.is_prod_or_staging ? 30 : 0

  tags = local.common_tags
}

# Application Configuration Secret Version
resource "aws_secretsmanager_secret_version" "app_config" {
  secret_id = aws_secretsmanager_secret.app_config.id
  secret_string = jsonencode({
    cloudflareApiToken = var.cloudflare_api_token
    testUserEmail      = var.test_user_email != "" ? var.test_user_email : "test@${var.environment}.gameflex.io"
    testUserPassword   = var.test_user_password != "" ? var.test_user_password : "Test123!"
    debugMode          = var.debug_mode != "" ? var.debug_mode : var.environment
    apiBaseUrl         = ""
    userPoolId         = aws_cognito_user_pool.main.id
    userPoolClientId   = aws_cognito_user_pool_client.main.id
  })

  lifecycle {
    ignore_changes = [secret_string]
  }
}

# GameFlex Backend - Terraform Configuration
# Main configuration file for AWS infrastructure

terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    archive = {
      source  = "hashicorp/archive"
      version = "~> 2.4"
    }
  }

  # Remote state backend configuration
  backend "s3" {
    # These values will be provided via backend config file or CLI
    # bucket         = "gameflex-terraform-state"
    # key            = "backend/terraform.tfstate"
    # region         = "us-west-2"
    # dynamodb_table = "gameflex-terraform-locks"
    # encrypt        = true
  }
}

# Configure the AWS Provider
provider "aws" {
  region = var.aws_region

  default_tags {
    tags = {
      Project     = var.project_name
      Environment = var.environment
      ManagedBy   = "Terraform"
    }
  }
}

# Data sources for current AWS account and region
data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

# Local values for resource naming
locals {
  name_prefix = "${var.project_name}-${var.environment}"
  
  # Common tags
  common_tags = {
    Project     = var.project_name
    Environment = var.environment
    ManagedBy   = "Terraform"
  }

  # DynamoDB table names
  table_names = {
    users           = "${local.name_prefix}-Users"
    posts           = "${local.name_prefix}-Posts"
    media           = "${local.name_prefix}-Media"
    user_profiles   = "${local.name_prefix}-UserProfiles"
    comments        = "${local.name_prefix}-Comments"
    likes           = "${local.name_prefix}-Likes"
    follows         = "${local.name_prefix}-Follows"
    channels        = "${local.name_prefix}-Channels"
    channel_members = "${local.name_prefix}-ChannelMembers"
    reflexes        = "${local.name_prefix}-Reflexes"
  }

  # Lambda function names
  function_names = {
    authorizer = "${local.name_prefix}-authorizer"
    auth       = "${local.name_prefix}-auth"
    posts      = "${local.name_prefix}-posts"
    media      = "${local.name_prefix}-media"
    users      = "${local.name_prefix}-users"
    health     = "${local.name_prefix}-health"
    reflexes   = "${local.name_prefix}-reflexes"
    channels   = "${local.name_prefix}-channels"
  }

  # Secret names
  secret_names = {
    r2_config    = "${local.name_prefix}-r2-config"
    app_config   = "${local.name_prefix}-app-config"
  }

  # Environment-specific settings
  is_production = var.environment == "production"
  is_staging    = var.environment == "staging"
  is_prod_or_staging = local.is_production || local.is_staging

  # DynamoDB settings based on environment
  deletion_protection_enabled = local.is_prod_or_staging
  point_in_time_recovery_enabled = local.is_prod_or_staging
}

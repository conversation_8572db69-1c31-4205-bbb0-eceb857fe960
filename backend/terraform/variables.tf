# GameFlex Backend - Terraform Variables
# Variable definitions for the infrastructure

variable "environment" {
  description = "Environment name (development, staging, production)"
  type        = string
  default     = "development"
  
  validation {
    condition     = contains(["development", "staging", "production"], var.environment)
    error_message = "Environment must be one of: development, staging, production."
  }
}

variable "project_name" {
  description = "Project name for resource naming"
  type        = string
  default     = "gameflex"
}

variable "aws_region" {
  description = "AWS region for resources"
  type        = string
  default     = "us-west-2"
}

# CloudFlare R2 Configuration
variable "r2_account_id" {
  description = "CloudFlare R2 Account ID"
  type        = string
  default     = ""
  sensitive   = true
}

variable "r2_access_key_id" {
  description = "CloudFlare R2 Access Key ID"
  type        = string
  default     = ""
  sensitive   = true
}

variable "r2_secret_access_key" {
  description = "CloudFlare R2 Secret Access Key"
  type        = string
  default     = ""
  sensitive   = true
}

variable "r2_endpoint" {
  description = "CloudFlare R2 Endpoint URL"
  type        = string
  default     = ""
}

variable "r2_bucket_name" {
  description = "CloudFlare R2 Bucket Name"
  type        = string
  default     = ""
}

variable "r2_public_url" {
  description = "CloudFlare R2 Public URL"
  type        = string
  default     = "https://pub-34709f09e8384ef1a67928492571c01d.r2.dev"
}

# Custom Domain Configuration
variable "domain_name" {
  description = "Custom domain name for API Gateway (e.g., staging.api.gameflex.io)"
  type        = string
  default     = ""
}

variable "certificate_arn" {
  description = "ACM Certificate ARN for custom domain (required for staging/production)"
  type        = string
  default     = ""
}

# Application Configuration
variable "cloudflare_api_token" {
  description = "CloudFlare API Token"
  type        = string
  default     = ""
  sensitive   = true
}

variable "test_user_email" {
  description = "Test user email for seeding"
  type        = string
  default     = ""
}

variable "test_user_password" {
  description = "Test user password for seeding"
  type        = string
  default     = ""
  sensitive   = true
}

variable "debug_mode" {
  description = "Debug mode setting"
  type        = string
  default     = ""
}

# Lambda Configuration
variable "lambda_runtime" {
  description = "Lambda runtime version"
  type        = string
  default     = "nodejs20.x"
}

variable "lambda_timeout" {
  description = "Lambda function timeout in seconds"
  type        = number
  default     = 30
}

variable "lambda_memory_size" {
  description = "Lambda function memory size in MB"
  type        = number
  default     = 256
}

# API Gateway Configuration
variable "api_stage_name" {
  description = "API Gateway stage name"
  type        = string
  default     = "v1"
}

# Import existing resources flag
variable "import_existing_resources" {
  description = "Set to true when importing existing resources into Terraform state"
  type        = bool
  default     = false
}

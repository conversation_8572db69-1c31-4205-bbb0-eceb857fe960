# GameFlex Backend - Lambda Functions
# All Lambda function configurations

# Common environment variables for all Lambda functions
locals {
  common_lambda_environment = {
    ENVIRONMENT               = var.environment
    PROJECT_NAME             = var.project_name
    USER_POOL_ID             = aws_cognito_user_pool.main.id
    USER_POOL_CLIENT_ID      = aws_cognito_user_pool_client.main.id
    POSTS_TABLE              = aws_dynamodb_table.posts.name
    MEDIA_TABLE              = aws_dynamodb_table.media.name
    USER_PROFILES_TABLE      = aws_dynamodb_table.user_profiles.name
    COMMENTS_TABLE           = aws_dynamodb_table.comments.name
    LIKES_TABLE              = aws_dynamodb_table.likes.name
    FOLLOWS_TABLE            = aws_dynamodb_table.follows.name
    CHANNELS_TABLE           = aws_dynamodb_table.channels.name
    CHANNEL_MEMBERS_TABLE    = aws_dynamodb_table.channel_members.name
    REFLEXES_TABLE           = aws_dynamodb_table.reflexes.name
    USERS_TABLE              = aws_dynamodb_table.users.name
    R2_SECRET_NAME           = aws_secretsmanager_secret.r2_config.name
    APP_CONFIG_SECRET_NAME   = aws_secretsmanager_secret.app_config.name
    # Legacy environment variables for backward compatibility
    R2_ACCOUNT_ID            = var.r2_account_id
    R2_ACCESS_KEY_ID         = var.r2_access_key_id
    R2_SECRET_ACCESS_KEY     = var.r2_secret_access_key
    R2_ENDPOINT              = var.r2_endpoint
    R2_BUCKET_NAME           = var.r2_bucket_name
    R2_PUBLIC_URL            = var.r2_public_url
  }
}

# Archive source code for Lambda functions
data "archive_file" "authorizer_zip" {
  type        = "zip"
  source_dir  = "${path.module}/../src/authorizer"
  output_path = "${path.module}/.terraform/tmp/authorizer.zip"
}

data "archive_file" "auth_zip" {
  type        = "zip"
  source_dir  = "${path.module}/../src/auth"
  output_path = "${path.module}/.terraform/tmp/auth.zip"
}

data "archive_file" "posts_zip" {
  type        = "zip"
  source_dir  = "${path.module}/../src/posts"
  output_path = "${path.module}/.terraform/tmp/posts.zip"
}

data "archive_file" "media_zip" {
  type        = "zip"
  source_dir  = "${path.module}/../src/media"
  output_path = "${path.module}/.terraform/tmp/media.zip"
}

data "archive_file" "users_zip" {
  type        = "zip"
  source_dir  = "${path.module}/../src/users"
  output_path = "${path.module}/.terraform/tmp/users.zip"
}

data "archive_file" "health_zip" {
  type        = "zip"
  source_dir  = "${path.module}/../src/health"
  output_path = "${path.module}/.terraform/tmp/health.zip"
}

data "archive_file" "reflexes_zip" {
  type        = "zip"
  source_dir  = "${path.module}/../src/reflexes"
  output_path = "${path.module}/.terraform/tmp/reflexes.zip"
}

data "archive_file" "channels_zip" {
  type        = "zip"
  source_dir  = "${path.module}/../src/channels"
  output_path = "${path.module}/.terraform/tmp/channels.zip"
}

# Authorizer Lambda Function
resource "aws_lambda_function" "authorizer" {
  filename         = data.archive_file.authorizer_zip.output_path
  function_name    = local.function_names.authorizer
  role            = aws_iam_role.lambda_execution_role.arn
  handler         = "index.handler"
  runtime         = "nodejs18.x"
  timeout         = var.lambda_timeout
  memory_size     = var.lambda_memory_size

  source_code_hash = data.archive_file.authorizer_zip.output_base64sha256

  environment {
    variables = merge(local.common_lambda_environment, {
      USER_POOL_ID = aws_cognito_user_pool.main.id
    })
  }

  tags = local.common_tags
}

# Auth Lambda Function
resource "aws_lambda_function" "auth" {
  filename         = data.archive_file.auth_zip.output_path
  function_name    = local.function_names.auth
  role            = aws_iam_role.lambda_execution_role.arn
  handler         = "index.handler"
  runtime         = var.lambda_runtime
  timeout         = var.lambda_timeout
  memory_size     = var.lambda_memory_size

  source_code_hash = data.archive_file.auth_zip.output_base64sha256

  environment {
    variables = local.common_lambda_environment
  }

  tags = local.common_tags
}

# Posts Lambda Function
resource "aws_lambda_function" "posts" {
  filename         = data.archive_file.posts_zip.output_path
  function_name    = local.function_names.posts
  role            = aws_iam_role.lambda_execution_role.arn
  handler         = "index.handler"
  runtime         = var.lambda_runtime
  timeout         = var.lambda_timeout
  memory_size     = var.lambda_memory_size

  source_code_hash = data.archive_file.posts_zip.output_base64sha256

  environment {
    variables = local.common_lambda_environment
  }

  tags = local.common_tags
}

# Media Lambda Function
resource "aws_lambda_function" "media" {
  filename         = data.archive_file.media_zip.output_path
  function_name    = local.function_names.media
  role            = aws_iam_role.lambda_execution_role.arn
  handler         = "index.handler"
  runtime         = var.lambda_runtime
  timeout         = var.lambda_timeout
  memory_size     = var.lambda_memory_size

  source_code_hash = data.archive_file.media_zip.output_base64sha256

  environment {
    variables = local.common_lambda_environment
  }

  tags = local.common_tags
}

# Users Lambda Function
resource "aws_lambda_function" "users" {
  filename         = data.archive_file.users_zip.output_path
  function_name    = local.function_names.users
  role            = aws_iam_role.lambda_execution_role.arn
  handler         = "index.handler"
  runtime         = var.lambda_runtime
  timeout         = var.lambda_timeout
  memory_size     = var.lambda_memory_size

  source_code_hash = data.archive_file.users_zip.output_base64sha256

  environment {
    variables = merge(local.common_lambda_environment, {
      USERS_TABLE         = aws_dynamodb_table.users.name
      USER_PROFILES_TABLE = aws_dynamodb_table.user_profiles.name
      FOLLOWS_TABLE       = aws_dynamodb_table.follows.name
      POSTS_TABLE         = aws_dynamodb_table.posts.name
      LIKES_TABLE         = aws_dynamodb_table.likes.name
      USER_POOL_ID        = aws_cognito_user_pool.main.id
      USER_POOL_CLIENT_ID = aws_cognito_user_pool_client.main.id
      REGION              = var.aws_region
    })
  }

  tags = local.common_tags
}

# Health Lambda Function
resource "aws_lambda_function" "health" {
  filename         = data.archive_file.health_zip.output_path
  function_name    = local.function_names.health
  role            = aws_iam_role.lambda_execution_role.arn
  handler         = "index.handler"
  runtime         = var.lambda_runtime
  timeout         = var.lambda_timeout
  memory_size     = var.lambda_memory_size

  source_code_hash = data.archive_file.health_zip.output_base64sha256

  environment {
    variables = local.common_lambda_environment
  }

  tags = local.common_tags
}

# Reflexes Lambda Function
resource "aws_lambda_function" "reflexes" {
  filename         = data.archive_file.reflexes_zip.output_path
  function_name    = local.function_names.reflexes
  role            = aws_iam_role.lambda_execution_role.arn
  handler         = "index.handler"
  runtime         = var.lambda_runtime
  timeout         = var.lambda_timeout
  memory_size     = var.lambda_memory_size

  source_code_hash = data.archive_file.reflexes_zip.output_base64sha256

  environment {
    variables = local.common_lambda_environment
  }

  tags = local.common_tags
}

# Channels Lambda Function
resource "aws_lambda_function" "channels" {
  filename         = data.archive_file.channels_zip.output_path
  function_name    = local.function_names.channels
  role            = aws_iam_role.lambda_execution_role.arn
  handler         = "index.handler"
  runtime         = var.lambda_runtime
  timeout         = var.lambda_timeout
  memory_size     = var.lambda_memory_size

  source_code_hash = data.archive_file.channels_zip.output_base64sha256

  environment {
    variables = local.common_lambda_environment
  }

  tags = local.common_tags
}

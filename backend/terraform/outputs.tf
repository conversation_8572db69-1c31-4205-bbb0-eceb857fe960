# GameFlex Backend - Terraform Outputs
# Output values for the infrastructure

# API Gateway
output "api_gateway_url" {
  description = "API Gateway endpoint URL"
  value       = var.domain_name != "" ? "https://${var.domain_name}/" : aws_api_gateway_deployment.main.invoke_url
}

output "api_gateway_id" {
  description = "API Gateway ID"
  value       = aws_api_gateway_rest_api.main.id
}

output "api_gateway_stage" {
  description = "API Gateway stage name"
  value       = var.api_stage_name
}

# Cognito
output "user_pool_id" {
  description = "Cognito User Pool ID"
  value       = aws_cognito_user_pool.main.id
}

output "user_pool_client_id" {
  description = "Cognito User Pool Client ID"
  value       = aws_cognito_user_pool_client.main.id
}

output "user_pool_arn" {
  description = "Cognito User Pool ARN"
  value       = aws_cognito_user_pool.main.arn
}

# Secrets Manager
output "r2_secret_name" {
  description = "AWS Secrets Manager secret name for R2 configuration"
  value       = aws_secretsmanager_secret.r2_config.name
}

output "r2_secret_arn" {
  description = "AWS Secrets Manager secret ARN for R2 configuration"
  value       = aws_secretsmanager_secret.r2_config.arn
}

output "app_config_secret_name" {
  description = "AWS Secrets Manager secret name for application configuration"
  value       = aws_secretsmanager_secret.app_config.name
}

output "app_config_secret_arn" {
  description = "AWS Secrets Manager secret ARN for application configuration"
  value       = aws_secretsmanager_secret.app_config.arn
}

# DynamoDB Tables
output "users_table_name" {
  description = "DynamoDB Users Table Name"
  value       = aws_dynamodb_table.users.name
}

output "posts_table_name" {
  description = "DynamoDB Posts Table Name"
  value       = aws_dynamodb_table.posts.name
}

output "media_table_name" {
  description = "DynamoDB Media Table Name"
  value       = aws_dynamodb_table.media.name
}

output "user_profiles_table_name" {
  description = "DynamoDB UserProfiles Table Name"
  value       = aws_dynamodb_table.user_profiles.name
}

output "comments_table_name" {
  description = "DynamoDB Comments Table Name"
  value       = aws_dynamodb_table.comments.name
}

output "likes_table_name" {
  description = "DynamoDB Likes Table Name"
  value       = aws_dynamodb_table.likes.name
}

output "follows_table_name" {
  description = "DynamoDB Follows Table Name"
  value       = aws_dynamodb_table.follows.name
}

output "channels_table_name" {
  description = "DynamoDB Channels Table Name"
  value       = aws_dynamodb_table.channels.name
}

output "channel_members_table_name" {
  description = "DynamoDB ChannelMembers Table Name"
  value       = aws_dynamodb_table.channel_members.name
}

output "reflexes_table_name" {
  description = "DynamoDB Reflexes Table Name"
  value       = aws_dynamodb_table.reflexes.name
}

# Lambda Functions
output "lambda_function_names" {
  description = "Map of Lambda function names"
  value = {
    authorizer = aws_lambda_function.authorizer.function_name
    auth       = aws_lambda_function.auth.function_name
    posts      = aws_lambda_function.posts.function_name
    media      = aws_lambda_function.media.function_name
    users      = aws_lambda_function.users.function_name
    health     = aws_lambda_function.health.function_name
    reflexes   = aws_lambda_function.reflexes.function_name
    channels   = aws_lambda_function.channels.function_name
  }
}

# Custom Domain (if configured)
output "custom_domain_name" {
  description = "Custom domain name for API Gateway"
  value       = var.domain_name != "" ? var.domain_name : null
}

# Environment Information
output "environment" {
  description = "Environment name"
  value       = var.environment
}

output "project_name" {
  description = "Project name"
  value       = var.project_name
}

output "aws_region" {
  description = "AWS region"
  value       = var.aws_region
}

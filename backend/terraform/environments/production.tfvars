# GameFlex Backend - Production Environment Configuration
# Terraform variables for production environment

# Environment settings
environment  = "production"
project_name = "gameflex"
aws_region   = "us-west-2"

# CloudFlare R2 Configuration (update with production values)
r2_account_id        = ""  # Update with production R2 account ID
r2_access_key_id     = ""  # Update with production R2 access key
r2_secret_access_key = ""  # Update with production R2 secret key
r2_endpoint          = ""  # Update with production R2 endpoint
r2_bucket_name       = "gameflex-production"
r2_public_url        = "https://media.gameflex.io"

# Application Configuration
cloudflare_api_token = ""  # Update with production CloudFlare API token
test_user_email      = "<EMAIL>"
test_user_password   = "ProductionTest123!"
debug_mode           = "production"

# Custom Domain Configuration
domain_name     = "api.gameflex.io"
certificate_arn = ""  # Update with production certificate ARN

# Lambda Configuration
lambda_runtime     = "nodejs20.x"
lambda_timeout     = 30
lambda_memory_size = 512  # Increased memory for production

# API Gateway Configuration
api_stage_name = "v1"

# Import existing resources flag
import_existing_resources = false

# GameFlex Backend - Staging Environment Configuration
# Terraform variables for staging environment

# Environment settings
environment  = "staging"
project_name = "gameflex"
aws_region   = "us-west-2"

# CloudFlare R2 Configuration (using same as development for testing)
r2_account_id        = "79ee497f37a6902472563e9f3fe8f451"
r2_access_key_id     = "ac20ad141b9a1ca9d2470e9b3c663320"
r2_secret_access_key = "62d049b50e3df9701f70b7897e336d87d03a50c4c1c370b8cb28c7f54c37e451"
r2_endpoint          = "https://79ee497f37a6902472563e9f3fe8f451.r2.cloudflarestorage.com"
r2_bucket_name       = "gameflex-staging"
r2_public_url        = "https://pub-34709f09e8384ef1a67928492571c01d.r2.dev"

# Application Configuration
cloudflare_api_token = "****************************************"
test_user_email      = "<EMAIL>"
test_user_password   = "StagingTest123!"
debug_mode           = "staging"

# Custom Domain Configuration (disabled for testing)
domain_name     = ""
certificate_arn = ""

# Lambda Configuration
lambda_runtime     = "nodejs20.x"
lambda_timeout     = 30
lambda_memory_size = 256

# API Gateway Configuration
api_stage_name = "v1"

# Import existing resources flag
import_existing_resources = false

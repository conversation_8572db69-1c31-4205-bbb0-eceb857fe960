# GameFlex Backend - DynamoDB Tables
# All DynamoDB table configurations

# Users Table
resource "aws_dynamodb_table" "users" {
  name           = local.table_names.users
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "id"

  attribute {
    name = "id"
    type = "S"
  }

  attribute {
    name = "email"
    type = "S"
  }

  attribute {
    name = "username"
    type = "S"
  }

  attribute {
    name = "cognito_user_id"
    type = "S"
  }

  global_secondary_index {
    name     = "EmailIndex"
    hash_key = "email"
    projection_type = "ALL"
  }

  global_secondary_index {
    name     = "UsernameIndex"
    hash_key = "username"
    projection_type = "ALL"
  }

  global_secondary_index {
    name     = "CognitoUserIdIndex"
    hash_key = "cognito_user_id"
    projection_type = "ALL"
  }

  deletion_protection_enabled = local.deletion_protection_enabled

  point_in_time_recovery {
    enabled = local.point_in_time_recovery_enabled
  }

  tags = local.common_tags
}

# Posts Table
resource "aws_dynamodb_table" "posts" {
  name           = local.table_names.posts
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "id"

  attribute {
    name = "id"
    type = "S"
  }

  deletion_protection_enabled = local.deletion_protection_enabled

  point_in_time_recovery {
    enabled = local.point_in_time_recovery_enabled
  }

  tags = local.common_tags
}

# Media Table
resource "aws_dynamodb_table" "media" {
  name           = local.table_names.media
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "id"

  attribute {
    name = "id"
    type = "S"
  }

  deletion_protection_enabled = local.deletion_protection_enabled

  point_in_time_recovery {
    enabled = local.point_in_time_recovery_enabled
  }

  tags = local.common_tags
}

# User Profiles Table
resource "aws_dynamodb_table" "user_profiles" {
  name           = local.table_names.user_profiles
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "user_id"

  attribute {
    name = "user_id"
    type = "S"
  }

  deletion_protection_enabled = local.deletion_protection_enabled

  point_in_time_recovery {
    enabled = local.point_in_time_recovery_enabled
  }

  tags = local.common_tags
}

# Comments Table
resource "aws_dynamodb_table" "comments" {
  name           = local.table_names.comments
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "id"

  attribute {
    name = "id"
    type = "S"
  }

  attribute {
    name = "post_id"
    type = "S"
  }

  global_secondary_index {
    name     = "post-id-index"
    hash_key = "post_id"
    projection_type = "ALL"
  }

  deletion_protection_enabled = local.deletion_protection_enabled

  point_in_time_recovery {
    enabled = local.point_in_time_recovery_enabled
  }

  tags = local.common_tags
}

# Likes Table
resource "aws_dynamodb_table" "likes" {
  name           = local.table_names.likes
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "post_id"
  range_key      = "user_id"

  attribute {
    name = "post_id"
    type = "S"
  }

  attribute {
    name = "user_id"
    type = "S"
  }

  global_secondary_index {
    name     = "user-id-index"
    hash_key = "user_id"
    projection_type = "ALL"
  }

  deletion_protection_enabled = local.deletion_protection_enabled

  point_in_time_recovery {
    enabled = local.point_in_time_recovery_enabled
  }

  tags = local.common_tags
}

# Follows Table
resource "aws_dynamodb_table" "follows" {
  name           = local.table_names.follows
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "follower_id"
  range_key      = "following_id"

  attribute {
    name = "follower_id"
    type = "S"
  }

  attribute {
    name = "following_id"
    type = "S"
  }

  deletion_protection_enabled = local.deletion_protection_enabled

  point_in_time_recovery {
    enabled = local.point_in_time_recovery_enabled
  }

  tags = local.common_tags
}

# Channels Table
resource "aws_dynamodb_table" "channels" {
  name           = local.table_names.channels
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "id"

  attribute {
    name = "id"
    type = "S"
  }

  attribute {
    name = "owner_id"
    type = "S"
  }

  global_secondary_index {
    name     = "owner-id-index"
    hash_key = "owner_id"
    projection_type = "ALL"
  }

  deletion_protection_enabled = local.deletion_protection_enabled

  point_in_time_recovery {
    enabled = local.point_in_time_recovery_enabled
  }

  tags = local.common_tags
}

# Channel Members Table
resource "aws_dynamodb_table" "channel_members" {
  name           = local.table_names.channel_members
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "channel_id"
  range_key      = "user_id"

  attribute {
    name = "channel_id"
    type = "S"
  }

  attribute {
    name = "user_id"
    type = "S"
  }

  global_secondary_index {
    name     = "user-id-index"
    hash_key = "user_id"
    projection_type = "ALL"
  }

  deletion_protection_enabled = local.deletion_protection_enabled

  point_in_time_recovery {
    enabled = local.point_in_time_recovery_enabled
  }

  tags = local.common_tags
}

# Reflexes Table
resource "aws_dynamodb_table" "reflexes" {
  name           = local.table_names.reflexes
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "id"

  attribute {
    name = "id"
    type = "S"
  }

  attribute {
    name = "post_id"
    type = "S"
  }

  attribute {
    name = "user_id"
    type = "S"
  }

  global_secondary_index {
    name     = "post-id-index"
    hash_key = "post_id"
    projection_type = "ALL"
  }

  global_secondary_index {
    name     = "user-id-index"
    hash_key = "user_id"
    projection_type = "ALL"
  }

  deletion_protection_enabled = local.deletion_protection_enabled

  point_in_time_recovery {
    enabled = local.point_in_time_recovery_enabled
  }

  tags = local.common_tags
}

#!/bin/bash

# GameFlex Backend - Terraform Deployment Script
# This script deploys the Terraform infrastructure for any environment
# Usage: ./deploy.sh <environment> [options]

set -e  # Exit on any error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print functions
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Usage function
usage() {
    echo "Usage: $0 <environment> [options]"
    echo ""
    echo "Environments:"
    echo "  development  - Deploy to development environment"
    echo "  staging      - Deploy to staging environment"
    echo "  production   - Deploy to production environment"
    echo ""
    echo "Options:"
    echo "  --init       - Initialize Terraform backend (first time setup)"
    echo "  --plan-only  - Only run terraform plan, don't apply"
    echo "  --auto-approve - Auto approve terraform apply (skip confirmation)"
    echo "  --destroy    - Destroy infrastructure instead of creating"
    echo "  --help       - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 development --init"
    echo "  $0 staging --plan-only"
    echo "  $0 production --auto-approve"
}

# Parse command line arguments
ENVIRONMENT=""
INIT_BACKEND=false
PLAN_ONLY=false
AUTO_APPROVE=false
DESTROY=false

while [[ $# -gt 0 ]]; do
    case $1 in
        development|staging|production)
            ENVIRONMENT="$1"
            shift
            ;;
        --init)
            INIT_BACKEND=true
            shift
            ;;
        --plan-only)
            PLAN_ONLY=true
            shift
            ;;
        --auto-approve)
            AUTO_APPROVE=true
            shift
            ;;
        --destroy)
            DESTROY=true
            shift
            ;;
        --help)
            usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Validate environment
if [ -z "$ENVIRONMENT" ]; then
    print_error "Environment is required"
    usage
    exit 1
fi

if [[ ! "$ENVIRONMENT" =~ ^(development|staging|production)$ ]]; then
    print_error "Invalid environment: $ENVIRONMENT"
    usage
    exit 1
fi

echo "🚀 Starting GameFlex Backend Terraform Deployment..."
echo "Environment: $ENVIRONMENT"
echo "=========================================="

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TERRAFORM_DIR="$(dirname "$SCRIPT_DIR")"
BACKEND_DIR="$SCRIPT_DIR/../.."

# Change to terraform directory
cd "$TERRAFORM_DIR"

# Check if Terraform is installed
if ! command -v terraform &> /dev/null; then
    print_error "Terraform is not installed"
    echo "Please install Terraform: https://www.terraform.io/downloads.html"
    exit 1
fi

# Check if AWS credentials are configured
print_status "Checking AWS credentials..."
if ! aws sts get-caller-identity > /dev/null 2>&1; then
    print_error "AWS credentials not configured or invalid"
    echo "   Please run 'aws configure' or set AWS environment variables"
    exit 1
fi

print_success "Prerequisites check passed"

# Initialize Terraform backend if requested
if [ "$INIT_BACKEND" = true ]; then
    print_status "Initializing Terraform backend for $ENVIRONMENT..."
    terraform init -backend-config="backend-configs/${ENVIRONMENT}.hcl" -reconfigure
    print_success "Terraform backend initialized"
fi

# Initialize Terraform (if not already done)
if [ ! -d ".terraform" ] || [ "$INIT_BACKEND" = true ]; then
    print_status "Initializing Terraform..."
    terraform init -backend-config="backend-configs/${ENVIRONMENT}.hcl"
    print_success "Terraform initialized"
fi

# Validate Terraform configuration
print_status "Validating Terraform configuration..."
terraform validate
print_success "Terraform configuration is valid"

# Function to import existing resources
import_existing_resources() {
    print_status "Checking for existing resources to import..."

    # Define the resources to check and import
    declare -A resources=(
        ["aws_dynamodb_table.posts"]="gameflex-${ENVIRONMENT}-Posts"
        ["aws_dynamodb_table.media"]="gameflex-${ENVIRONMENT}-Media"
        ["aws_dynamodb_table.user_profiles"]="gameflex-${ENVIRONMENT}-UserProfiles"
        ["aws_dynamodb_table.comments"]="gameflex-${ENVIRONMENT}-Comments"
        ["aws_dynamodb_table.likes"]="gameflex-${ENVIRONMENT}-Likes"
        ["aws_dynamodb_table.follows"]="gameflex-${ENVIRONMENT}-Follows"
        ["aws_dynamodb_table.channels"]="gameflex-${ENVIRONMENT}-Channels"
        ["aws_dynamodb_table.channel_members"]="gameflex-${ENVIRONMENT}-ChannelMembers"
        ["aws_dynamodb_table.reflexes"]="gameflex-${ENVIRONMENT}-Reflexes"
        ["aws_dynamodb_table.users"]="gameflex-${ENVIRONMENT}-Users"
    )

    local imported_count=0

    for resource in "${!resources[@]}"; do
        local table_name="${resources[$resource]}"

        # Check if table exists in AWS
        if aws dynamodb describe-table --table-name "$table_name" >/dev/null 2>&1; then
            # Check if resource is already in Terraform state by trying to plan
            local plan_output
            plan_output=$(terraform plan -var-file="environments/${ENVIRONMENT}.tfvars" -target="$resource" 2>&1)

            # If plan shows the resource will be created, it needs to be imported
            if echo "$plan_output" | grep -q "will be created"; then
                print_status "Importing existing table: $table_name"
                if terraform import -var-file="environments/${ENVIRONMENT}.tfvars" "$resource" "$table_name" >/dev/null 2>&1; then
                    print_success "✅ Imported $table_name"
                    ((imported_count++))
                else
                    print_warning "⚠️  Failed to import $table_name (may not be compatible)"
                fi
            else
                print_status "✅ $table_name already managed by Terraform"
            fi
        fi
    done

    if [ $imported_count -gt 0 ]; then
        print_success "Imported $imported_count existing resources"
    else
        print_status "No existing resources found to import"
    fi
}

# Import existing resources if they exist
import_existing_resources

# Plan the deployment
print_status "Planning Terraform deployment..."
if [ "$DESTROY" = true ]; then
    terraform plan -destroy -var-file="environments/${ENVIRONMENT}.tfvars" -out="${ENVIRONMENT}-destroy.tfplan"
    PLAN_FILE="${ENVIRONMENT}-destroy.tfplan"
else
    terraform plan -var-file="environments/${ENVIRONMENT}.tfvars" -out="${ENVIRONMENT}.tfplan"
    PLAN_FILE="${ENVIRONMENT}.tfplan"
fi

print_success "Terraform plan completed"

# Exit if plan-only
if [ "$PLAN_ONLY" = true ]; then
    print_success "Plan-only mode: Terraform plan completed successfully"
    echo "Plan file saved as: $PLAN_FILE"
    exit 0
fi

# Apply the plan
if [ "$DESTROY" = true ]; then
    print_warning "⚠️  DESTROYING INFRASTRUCTURE ⚠️"
    echo "This will destroy all resources in the $ENVIRONMENT environment!"
    if [ "$AUTO_APPROVE" = false ]; then
        read -p "Are you sure you want to destroy? Type 'yes' to confirm: " -r
        if [[ ! $REPLY == "yes" ]]; then
            echo "Destruction cancelled."
            exit 1
        fi
    fi
    print_status "Destroying infrastructure..."
    terraform apply "$PLAN_FILE"
    print_success "Infrastructure destroyed successfully!"
else
    print_status "Applying Terraform plan..."
    if [ "$AUTO_APPROVE" = false ]; then
        terraform apply "$PLAN_FILE"
    else
        terraform apply -auto-approve "$PLAN_FILE"
    fi
    print_success "Deployment completed successfully!"
fi

# Clean up plan file
rm -f "$PLAN_FILE"

# Show outputs
if [ "$DESTROY" = false ]; then
    print_status "Deployment Information:"
    echo "=========================="
    terraform output
fi

echo ""
print_success "🎉 Terraform deployment complete!"

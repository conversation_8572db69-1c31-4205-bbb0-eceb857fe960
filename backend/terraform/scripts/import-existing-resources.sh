#!/bin/bash

# GameFlex Backend - Import Existing Resources Script
# This script checks for existing AWS resources and imports them into Terraform state
# Usage: ./import-existing-resources.sh <environment>

# Don't exit on error, handle errors gracefully
set +e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print functions
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Usage function
usage() {
    echo "Usage: $0 <environment>"
    echo ""
    echo "Environments:"
    echo "  development  - Import development resources"
    echo "  staging      - Import staging resources"
    echo "  production   - Import production resources"
}

# Parse command line arguments
ENVIRONMENT=""

while [[ $# -gt 0 ]]; do
    case $1 in
        development|staging|production)
            ENVIRONMENT="$1"
            shift
            ;;
        --help)
            usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Validate environment
if [ -z "$ENVIRONMENT" ]; then
    print_error "Environment is required"
    usage
    exit 1
fi

echo "🔄 Importing Existing Resources for $ENVIRONMENT..."
echo "=================================================="

# Set variables
PROJECT_NAME="gameflex"
NAME_PREFIX="${PROJECT_NAME}-${ENVIRONMENT}"

# Function to check if resource exists in Terraform state
resource_in_state() {
    local resource="$1"
    timeout 10 terraform state show "$resource" >/dev/null 2>&1
}

# Function to import resource if it exists in AWS but not in Terraform state
import_if_exists() {
    local resource_type="$1"
    local terraform_resource="$2"
    local aws_identifier="$3"
    local check_command="$4"

    print_status "Checking $resource_type: $aws_identifier"

    # Check if resource exists in AWS with timeout
    if timeout 10 bash -c "$check_command" >/dev/null 2>&1; then
        # Check if resource is already in Terraform state
        if ! resource_in_state "$terraform_resource"; then
            print_status "Importing $resource_type: $aws_identifier"
            if timeout 30 terraform import -var-file="environments/${ENVIRONMENT}.tfvars" "$terraform_resource" "$aws_identifier" >/dev/null 2>&1; then
                print_success "✅ Imported $aws_identifier"
                return 0
            else
                print_warning "⚠️  Failed to import $aws_identifier"
                return 1
            fi
        else
            print_status "✅ $aws_identifier already in Terraform state"
            return 0
        fi
    else
        print_status "ℹ️  $aws_identifier does not exist in AWS (or check timed out)"
        return 0
    fi
}

# Initialize counters
imported_count=0
failed_count=0

print_status "Checking and importing DynamoDB tables..."

# DynamoDB Tables
declare -A dynamodb_tables=(
    ["aws_dynamodb_table.users"]="${NAME_PREFIX}-Users"
    ["aws_dynamodb_table.posts"]="${NAME_PREFIX}-Posts"
    ["aws_dynamodb_table.media"]="${NAME_PREFIX}-Media"
    ["aws_dynamodb_table.user_profiles"]="${NAME_PREFIX}-UserProfiles"
    ["aws_dynamodb_table.comments"]="${NAME_PREFIX}-Comments"
    ["aws_dynamodb_table.likes"]="${NAME_PREFIX}-Likes"
    ["aws_dynamodb_table.follows"]="${NAME_PREFIX}-Follows"
    ["aws_dynamodb_table.channels"]="${NAME_PREFIX}-Channels"
    ["aws_dynamodb_table.channel_members"]="${NAME_PREFIX}-ChannelMembers"
    ["aws_dynamodb_table.reflexes"]="${NAME_PREFIX}-Reflexes"
)

for terraform_resource in "${!dynamodb_tables[@]}"; do
    table_name="${dynamodb_tables[$terraform_resource]}"
    if import_if_exists "DynamoDB Table" "$terraform_resource" "$table_name" "aws dynamodb describe-table --table-name $table_name"; then
        ((imported_count++))
    else
        ((failed_count++))
    fi
done

print_status "Checking and importing Cognito resources..."

# Cognito User Pool (need to find by name)
user_pool_name="${NAME_PREFIX}-users"
print_status "Looking up Cognito User Pool: $user_pool_name"

user_pool_id=$(timeout 15 aws cognito-idp list-user-pools --max-results 60 --query "UserPools[?Name=='$user_pool_name'].Id" --output text 2>/dev/null || echo "")

if [ -n "$user_pool_id" ] && [ "$user_pool_id" != "None" ] && [ "$user_pool_id" != "" ]; then
    if import_if_exists "Cognito User Pool" "aws_cognito_user_pool.main" "$user_pool_id" "aws cognito-idp describe-user-pool --user-pool-id '$user_pool_id'"; then
        ((imported_count++))

        # Get User Pool Client ID
        print_status "Looking up Cognito User Pool Client for pool: $user_pool_id"
        client_id=$(timeout 10 aws cognito-idp list-user-pool-clients --user-pool-id "$user_pool_id" --query "UserPoolClients[0].ClientId" --output text 2>/dev/null || echo "")

        if [ -n "$client_id" ] && [ "$client_id" != "None" ] && [ "$client_id" != "" ]; then
            client_identifier="${user_pool_id}/${client_id}"
            if import_if_exists "Cognito User Pool Client" "aws_cognito_user_pool_client.main" "$client_identifier" "aws cognito-idp describe-user-pool-client --user-pool-id '$user_pool_id' --client-id '$client_id'"; then
                ((imported_count++))
            else
                ((failed_count++))
            fi
        else
            print_status "ℹ️  No User Pool Client found for pool $user_pool_id"
        fi
    else
        ((failed_count++))
    fi
else
    print_status "ℹ️  Cognito User Pool $user_pool_name not found"
fi

print_status "Checking and importing Secrets Manager secrets..."

# Secrets Manager
declare -A secrets=(
    ["aws_secretsmanager_secret.r2_config"]="${NAME_PREFIX}-r2-config"
    ["aws_secretsmanager_secret.app_config"]="${NAME_PREFIX}-app-config"
)

for terraform_resource in "${!secrets[@]}"; do
    secret_name="${secrets[$terraform_resource]}"
    if import_if_exists "Secrets Manager Secret" "$terraform_resource" "$secret_name" "aws secretsmanager describe-secret --secret-id $secret_name"; then
        ((imported_count++))
    else
        ((failed_count++))
    fi
done

print_status "Checking and importing IAM resources..."

# IAM Roles
declare -A iam_roles=(
    ["aws_iam_role.lambda_execution_role"]="${NAME_PREFIX}-lambda-execution-role"
    ["aws_iam_role.api_gateway_execution_role"]="${NAME_PREFIX}-api-gateway-execution-role"
)

for terraform_resource in "${!iam_roles[@]}"; do
    role_name="${iam_roles[$terraform_resource]}"
    if import_if_exists "IAM Role" "$terraform_resource" "$role_name" "aws iam get-role --role-name $role_name"; then
        ((imported_count++))
    else
        ((failed_count++))
    fi
done

# IAM Policies
declare -A iam_policies=(
    ["aws_iam_policy.dynamodb_access"]="${NAME_PREFIX}-dynamodb-access"
    ["aws_iam_policy.cognito_access"]="${NAME_PREFIX}-cognito-access"
    ["aws_iam_policy.secrets_manager_access"]="${NAME_PREFIX}-secrets-manager-access"
    ["aws_iam_policy.api_gateway_lambda_invoke"]="${NAME_PREFIX}-api-gateway-lambda-invoke"
)

for terraform_resource in "${!iam_policies[@]}"; do
    policy_name="${iam_policies[$terraform_resource]}"
    print_status "Looking up IAM Policy ARN for: $policy_name"

    # Get policy ARN with timeout
    policy_arn=$(timeout 10 aws iam list-policies --scope Local --query "Policies[?PolicyName=='$policy_name'].Arn" --output text 2>/dev/null || echo "")

    if [ -n "$policy_arn" ] && [ "$policy_arn" != "None" ] && [ "$policy_arn" != "" ]; then
        if import_if_exists "IAM Policy" "$terraform_resource" "$policy_arn" "aws iam get-policy --policy-arn '$policy_arn'"; then
            ((imported_count++))
        else
            ((failed_count++))
        fi
    else
        print_status "ℹ️  IAM Policy $policy_name not found or lookup failed"
    fi
done

print_status "Checking and importing Lambda functions..."

# Lambda Functions
declare -A lambda_functions=(
    ["aws_lambda_function.authorizer"]="${NAME_PREFIX}-authorizer"
    ["aws_lambda_function.auth"]="${NAME_PREFIX}-auth"
    ["aws_lambda_function.posts"]="${NAME_PREFIX}-posts"
    ["aws_lambda_function.media"]="${NAME_PREFIX}-media"
    ["aws_lambda_function.users"]="${NAME_PREFIX}-users"
    ["aws_lambda_function.health"]="${NAME_PREFIX}-health"
    ["aws_lambda_function.reflexes"]="${NAME_PREFIX}-reflexes"
    ["aws_lambda_function.channels"]="${NAME_PREFIX}-channels"
)

for terraform_resource in "${!lambda_functions[@]}"; do
    function_name="${lambda_functions[$terraform_resource]}"
    if import_if_exists "Lambda Function" "$terraform_resource" "$function_name" "aws lambda get-function --function-name $function_name"; then
        ((imported_count++))
    else
        ((failed_count++))
    fi
done

# Summary
echo ""
print_success "Import Summary:"
echo "==============="
echo "✅ Successfully imported: $imported_count resources"
if [ $failed_count -gt 0 ]; then
    echo "⚠️  Failed to import: $failed_count resources"
else
    echo "🎉 All existing resources imported successfully!"
fi

echo ""
print_status "Next steps:"
echo "1. Run terraform plan to see remaining resources to create"
echo "2. Run terraform apply to deploy missing resources"
echo ""

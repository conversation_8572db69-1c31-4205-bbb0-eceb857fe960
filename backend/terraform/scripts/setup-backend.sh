#!/bin/bash

# GameFlex Backend - Terraform State Backend Setup
# This script creates the S3 bucket and DynamoDB table for Terraform state management
# Usage: ./setup-backend.sh <environment>

set -e  # Exit on any error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print functions
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Usage function
usage() {
    echo "Usage: $0 <environment>"
    echo ""
    echo "Environments:"
    echo "  development  - Setup backend for development environment"
    echo "  staging      - Setup backend for staging environment"
    echo "  production   - Setup backend for production environment"
    echo ""
    echo "This script creates:"
    echo "  - S3 bucket for Terraform state storage"
    echo "  - DynamoDB table for state locking"
    echo "  - Proper bucket policies and versioning"
}

# Parse command line arguments
ENVIRONMENT=""

while [[ $# -gt 0 ]]; do
    case $1 in
        development|staging|production)
            ENVIRONMENT="$1"
            shift
            ;;
        --help)
            usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Validate environment
if [ -z "$ENVIRONMENT" ]; then
    print_error "Environment is required"
    usage
    exit 1
fi

echo "🔧 Setting up Terraform Backend for $ENVIRONMENT..."
echo "=================================================="

# Set variables based on environment
BUCKET_NAME="gameflex-terraform-state-${ENVIRONMENT}"
DYNAMODB_TABLE="gameflex-terraform-locks-${ENVIRONMENT}"
REGION="us-west-2"

# Check if AWS credentials are configured
print_status "Checking AWS credentials..."
if ! aws sts get-caller-identity > /dev/null 2>&1; then
    print_error "AWS credentials not configured or invalid"
    echo "   Please run 'aws configure' or set AWS environment variables"
    exit 1
fi

print_success "AWS credentials configured"

# Create S3 bucket for state storage
print_status "Creating S3 bucket: $BUCKET_NAME"

if aws s3api head-bucket --bucket "$BUCKET_NAME" 2>/dev/null; then
    print_warning "S3 bucket already exists: $BUCKET_NAME"
else
    # Create bucket
    if [ "$REGION" = "us-east-1" ]; then
        aws s3api create-bucket --bucket "$BUCKET_NAME" --region "$REGION"
    else
        aws s3api create-bucket --bucket "$BUCKET_NAME" --region "$REGION" \
            --create-bucket-configuration LocationConstraint="$REGION"
    fi
    print_success "S3 bucket created: $BUCKET_NAME"
fi

# Enable versioning
print_status "Enabling versioning on S3 bucket..."
aws s3api put-bucket-versioning --bucket "$BUCKET_NAME" \
    --versioning-configuration Status=Enabled
print_success "Versioning enabled"

# Enable server-side encryption
print_status "Enabling server-side encryption..."
aws s3api put-bucket-encryption --bucket "$BUCKET_NAME" \
    --server-side-encryption-configuration '{
        "Rules": [
            {
                "ApplyServerSideEncryptionByDefault": {
                    "SSEAlgorithm": "AES256"
                }
            }
        ]
    }'
print_success "Server-side encryption enabled"

# Block public access
print_status "Blocking public access..."
aws s3api put-public-access-block --bucket "$BUCKET_NAME" \
    --public-access-block-configuration \
    BlockPublicAcls=true,IgnorePublicAcls=true,BlockPublicPolicy=true,RestrictPublicBuckets=true
print_success "Public access blocked"

# Create DynamoDB table for state locking
print_status "Creating DynamoDB table: $DYNAMODB_TABLE"

if aws dynamodb describe-table --table-name "$DYNAMODB_TABLE" --region "$REGION" 2>/dev/null; then
    print_warning "DynamoDB table already exists: $DYNAMODB_TABLE"
else
    aws dynamodb create-table \
        --table-name "$DYNAMODB_TABLE" \
        --attribute-definitions AttributeName=LockID,AttributeType=S \
        --key-schema AttributeName=LockID,KeyType=HASH \
        --billing-mode PAY_PER_REQUEST \
        --region "$REGION" \
        --tags Key=Project,Value=gameflex Key=Environment,Value="$ENVIRONMENT" Key=ManagedBy,Value=Terraform
    
    # Wait for table to be active
    print_status "Waiting for DynamoDB table to be active..."
    aws dynamodb wait table-exists --table-name "$DYNAMODB_TABLE" --region "$REGION"
    print_success "DynamoDB table created: $DYNAMODB_TABLE"
fi

# Summary
print_success "Terraform backend setup completed!"
echo ""
echo "📋 Backend Configuration:"
echo "========================="
echo "S3 Bucket: $BUCKET_NAME"
echo "DynamoDB Table: $DYNAMODB_TABLE"
echo "Region: $REGION"
echo ""
echo "🔧 Next Steps:"
echo "=============="
echo "1. Initialize Terraform with the backend:"
echo "   cd terraform"
echo "   terraform init -backend-config=\"backend-configs/${ENVIRONMENT}.hcl\""
echo ""
echo "2. Deploy the infrastructure:"
echo "   ../deploy-terraform-${ENVIRONMENT}.sh"
echo ""
echo "🎉 Backend setup complete!"

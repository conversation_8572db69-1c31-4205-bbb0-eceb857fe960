# GameFlex Backend - Terraform Infrastructure

A serverless backend for GameFlex built with Terraform for infrastructure deployment and management. This replaces the previous AWS SAM setup with a more flexible and maintainable Infrastructure as Code approach.

## Overview

The Terraform configuration deploys the following AWS resources:

- **DynamoDB Tables**: All application tables (Users, Posts, Media, etc.)
- **Cognito User Pool**: Authentication and user management
- **Lambda Functions**: All backend API functions
- **API Gateway**: REST API with proper routing and authorization
- **Secrets Manager**: Secure storage for R2 and application configuration
- **IAM Roles & Policies**: Proper permissions for all resources

## Quick Start

### Prerequisites

1. **Terraform**: Install Terraform >= 1.0
   ```bash
   # macOS
   brew install terraform

   # Linux
   wget https://releases.hashicorp.com/terraform/1.6.0/terraform_1.6.0_linux_amd64.zip
   unzip terraform_1.6.0_linux_amd64.zip
   sudo mv terraform /usr/local/bin/
   ```

2. **AWS CLI**: Configure AWS credentials
   ```bash
   aws configure
   # Or set environment variables:
   export AWS_ACCESS_KEY_ID=your_key
   export AWS_SECRET_ACCESS_KEY=your_secret
   export AWS_DEFAULT_REGION=us-west-2
   ```

3. **Node.js**: For Lambda function packaging (already installed)

### Deploy Development Environment

```bash
# Deploy infrastructure
./deploy.sh

# Seed database with test data
./seed.sh

# Start local development (optional)
./start.sh
```

### Deploy Other Environments

```bash
# Deploy to staging
./deploy-staging.sh
./seed.sh staging

# Deploy to production
./deploy-production.sh
./seed.sh production
```

## Directory Structure

```
backend/
├── deploy.sh                 # Deploy development environment
├── deploy-staging.sh         # Deploy staging environment
├── deploy-production.sh      # Deploy production environment
├── seed.sh                   # Seed database with test data
├── start.sh                  # Start local development server
├── stop.sh                   # Stop local development server
├── terraform/                # Terraform infrastructure configuration
│   ├── main.tf              # Main Terraform configuration
│   ├── variables.tf         # Variable definitions
│   ├── outputs.tf           # Output definitions
│   ├── dynamodb.tf          # DynamoDB table configurations
│   ├── cognito.tf           # Cognito User Pool configuration
│   ├── secrets.tf           # AWS Secrets Manager configuration
│   ├── iam.tf               # IAM roles and policies
│   ├── lambda.tf            # Lambda function configurations
│   ├── api_gateway.tf       # API Gateway main configuration
│   ├── api_routes.tf        # API Gateway routes and integrations
│   ├── environments/        # Environment-specific configurations
│   │   ├── development.tfvars
│   │   ├── staging.tfvars
│   │   └── production.tfvars
│   ├── backend-configs/     # Terraform backend configurations
│   │   ├── development.hcl
│   │   ├── staging.hcl
│   │   └── production.hcl
│   └── scripts/             # Deployment and utility scripts
│       ├── deploy.sh
│       └── setup-backend.sh
├── src/                     # Lambda function source code
│   ├── auth/               # Authentication functions
│   ├── posts/              # Posts management
│   ├── media/              # Media handling
│   ├── users/              # User management
│   ├── channels/           # Channel management
│   ├── reflexes/           # Reflexes functionality
│   ├── health/             # Health check
│   ├── authorizer/         # API Gateway authorizer
│   └── utils/              # Shared utilities
├── scripts/                # Utility scripts
│   ├── seed-data.sh        # Database seeding script
│   ├── run-tests.sh        # Test runner
│   └── install-dependencies.sh
└── tests/                  # Test suites
    ├── unit/               # Unit tests
    ├── integration/        # Integration tests
    └── utils/              # Test utilities
```

## Environment Management

### Development Environment

- **Purpose**: Local development and testing
- **Configuration**: `terraform/environments/development.tfvars`
- **Domain**: Uses API Gateway URL (no custom domain)
- **Data Protection**: Disabled (tables can be deleted)

```bash
./deploy.sh
```

### Staging Environment

- **Purpose**: Pre-production testing
- **Configuration**: `terraform/environments/staging.tfvars`
- **Domain**: `staging.api.gameflex.io`
- **Data Protection**: Enabled (deletion protection, point-in-time recovery)

```bash
./deploy-staging.sh
```

### Production Environment

- **Purpose**: Live production environment
- **Configuration**: `terraform/environments/production.tfvars`
- **Domain**: `api.gameflex.io`
- **Data Protection**: Enabled (deletion protection, point-in-time recovery)

```bash
./deploy-production.sh
```

## Configuration

### Environment Variables

Update the `.tfvars` files in the `terraform/environments/` directory with your specific configuration:

```hcl
# CloudFlare R2 Configuration
r2_account_id        = "your_r2_account_id"
r2_access_key_id     = "your_r2_access_key"
r2_secret_access_key = "your_r2_secret_key"
r2_endpoint          = "https://your_account_id.r2.cloudflarestorage.com"
r2_bucket_name       = "gameflex-environment"
r2_public_url        = "https://your_r2_public_url"

# Custom Domain (staging/production)
domain_name     = "staging.api.gameflex.io"
certificate_arn = "arn:aws:acm:us-east-1:account:certificate/cert-id"
```

### Secrets Management

Sensitive data is stored in AWS Secrets Manager:

- **R2 Configuration**: CloudFlare R2 credentials and settings
- **Application Configuration**: API tokens, test user credentials, etc.

Update secrets after deployment:

```bash
# Update R2 configuration
aws secretsmanager put-secret-value \
  --secret-id gameflex-r2-config-development \
  --secret-string '{"accountId":"...","accessKeyId":"...","secretAccessKey":"...","endpoint":"...","bucketName":"...","publicUrl":"..."}'

# Update application configuration
aws secretsmanager put-secret-value \
  --secret-id gameflex-app-config-development \
  --secret-string '{"cloudflareApiToken":"...","testUserEmail":"...","testUserPassword":"..."}'
```

## Available Scripts

### Root-Level Scripts

```bash
# Deployment
./deploy.sh              # Deploy development environment
./deploy-staging.sh      # Deploy staging environment
./deploy-production.sh   # Deploy production environment

# Data seeding
./seed.sh               # Seed development environment
./seed.sh staging       # Seed staging environment
./seed.sh production    # Seed production environment

# Local development
./start.sh              # Start local development server
./stop.sh               # Stop local development server
```

### NPM Scripts

```bash
# Deployment
npm run deploy              # Deploy development
npm run deploy:staging      # Deploy staging
npm run deploy:production   # Deploy production

# Data seeding
npm run seed               # Seed development data
npm run seed:staging       # Seed staging data
npm run seed:production    # Seed production data

# Testing
npm run test               # Run all tests
npm run test:unit          # Run unit tests
npm run test:integration   # Run integration tests
npm run test:coverage      # Run tests with coverage

# Terraform utilities
npm run terraform:init      # Initialize Terraform
npm run terraform:plan      # Plan changes
npm run terraform:apply     # Apply changes
npm run terraform:destroy   # Destroy infrastructure
npm run terraform:validate  # Validate configuration
npm run terraform:fmt       # Format configuration
```

### Terraform Scripts

```bash
# Backend setup (first time only)
./terraform/scripts/setup-backend.sh <environment>

# Advanced deployment
./terraform/scripts/deploy.sh <environment> [options]

# Options:
#   --init       - Initialize Terraform backend
#   --plan-only  - Only run terraform plan
#   --auto-approve - Auto approve terraform apply
#   --destroy    - Destroy infrastructure
```

## Services

### Lambda Functions

1. **Auth Service** (`/auth`)
   - User registration and authentication
   - JWT token management
   - Password reset functionality

2. **Posts Service** (`/posts`)
   - Create, read, update, delete posts
   - Post publishing and draft management
   - Media attachment to posts

3. **Media Service** (`/media`)
   - File upload to CloudFlare R2
   - Media metadata management
   - Presigned URL generation

4. **Users Service** (`/users`)
   - User profile management
   - Follow/unfollow functionality
   - User discovery

5. **Channels Service** (`/channels`)
   - Channel creation and management
   - Channel membership
   - Channel-specific posts

6. **Reflexes Service** (`/reflexes`)
   - Quick reactions to posts
   - Reflex management

7. **Health Service** (`/health`)
   - System health checks
   - API status monitoring

8. **Authorizer Service**
   - API Gateway Lambda authorizer
   - JWT token validation

## Testing

Run tests against the deployed infrastructure:

```bash
# Run all tests
npm run test

# Run integration tests against localhost:3000
npm run test:integration

# Run tests with coverage
npm run test:coverage
```

## Troubleshooting

### Common Issues

1. **Backend Not Initialized**
   ```bash
   ./terraform/scripts/setup-backend.sh development
   ```

2. **State Lock Issues**
   ```bash
   cd terraform
   terraform force-unlock <lock-id>
   ```

3. **Resource Already Exists**
   - Use Terraform import for existing resources
   - Or destroy and recreate (development only)

4. **Lambda Function Updates**
   - Terraform automatically detects source code changes
   - Functions are redeployed when source changes

### Importing Existing Resources

If you have existing SAM resources, you can import them:

```bash
# Example: Import existing DynamoDB table
terraform import aws_dynamodb_table.users gameflex-development-Users

# Example: Import existing Cognito User Pool
terraform import aws_cognito_user_pool.main us-west-2_jZQ65BOBt
```

## Migration from SAM

To migrate from the existing SAM setup, see [TERRAFORM_MIGRATION.md](TERRAFORM_MIGRATION.md) for detailed instructions.

## Security Considerations

- All sensitive data is stored in AWS Secrets Manager
- IAM roles follow least privilege principle
- DynamoDB tables have deletion protection in staging/production
- S3 state bucket has versioning and encryption enabled
- API Gateway uses Lambda authorizers for authentication

## Support

For issues or questions:

1. Check the troubleshooting section above
2. Review Terraform logs: `terraform apply` output
3. Check AWS CloudWatch logs for Lambda functions
4. Verify AWS credentials and permissions

## Services

### Lambda Functions

1. **Auth Service** (`/auth`)
   - User registration and authentication
   - JWT token management
   - Password management

2. **Posts Service** (`/posts`)
   - Create, read, update, delete posts
   - Like/unlike functionality
   - Post management

3. **Media Service** (`/media`)
   - File upload with presigned URLs
   - Media metadata management
   - S3 integration

4. **Users Service** (`/users`)
   - User profile management
   - Follow/unfollow functionality
   - User data management

5. **Health Service** (`/health`)
   - System health checks
   - Service status monitoring

### Database Tables

- **Users**: User account information
- **UserProfiles**: Extended user profile data
- **Posts**: User posts and content
- **Comments**: Post comments
- **Likes**: Post likes tracking
- **Follows**: User follow relationships
- **Media**: Media file metadata

### S3 Buckets

- **Media Bucket**: User-generated content
- **Avatars Bucket**: User profile pictures
- **Temp Bucket**: Temporary file storage

## Prerequisites

- **Docker**: For running Lambda functions locally
- **Node.js**: Version 18 or higher
- **AWS SAM CLI**: For local development and deployment

### Installation

1. **Install Docker**
   ```bash
   # Follow Docker installation guide for your OS
   # https://docs.docker.com/get-docker/
   ```

2. **Install Node.js**
   ```bash
   # Download from https://nodejs.org/
   # Or use a version manager like nvm
   ```

3. **Install AWS SAM CLI**
   ```bash
   # macOS
   brew install aws-sam-cli
   
   # Linux
   pip install aws-sam-cli
   
   # Windows
   # Download from https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/install-sam-cli.html
   ```

## Quick Start

1. **Clone and navigate to the backend directory**
   ```bash
   cd backend
   ```

2. **Make scripts executable**
   ```bash
   chmod +x *.sh scripts/*.sh
   ```

3. **Start the backend**
   ```bash
   ./start.sh
   ```

   This will:
   - Install Lambda function dependencies
   - Start SAM local API server
   - Make API endpoints available locally

4. **Access the API**
   - API Gateway: http://localhost:3000

5. **Stop the backend**
   ```bash
   ./stop.sh
   ```

## API Endpoints

### Authentication
- `POST /auth/signup` - User registration
- `POST /auth/signin` - User login
- `POST /auth/refresh` - Refresh JWT token

### Posts
- `GET /posts` - Get all posts
- `POST /posts` - Create a new post
- `GET /posts/{id}` - Get specific post
- `PUT /posts/{id}` - Update post
- `DELETE /posts/{id}` - Delete post
- `POST /posts/{id}/like` - Like a post
- `DELETE /posts/{id}/like` - Unlike a post

### Media
- `POST /media/upload` - Get upload URL
- `GET /media/{id}` - Get media info
- `DELETE /media/{id}` - Delete media

### Users
- `GET /users/profile` - Get user profile
- `PUT /users/profile` - Update user profile
- `GET /users/{id}` - Get user by ID
- `POST /users/{id}/follow` - Follow user
- `DELETE /users/{id}/follow` - Unfollow user

### Health
- `GET /health` - System health check

## Local Development

This SAM backend runs locally and connects to your configured AWS services or can be deployed to AWS for production use.

## Development

### Project Structure
```
backend/
├── template.yaml           # SAM template
├── package.json            # Root package.json
├── start.sh               # Startup script
├── stop.sh                # Stop script
├── src/                   # Lambda function source code
│   ├── auth/              # Authentication service
│   ├── posts/             # Posts service
│   ├── media/             # Media service
│   ├── users/             # Users service
│   └── health/            # Health check service
├── scripts/               # Utility scripts
│   ├── init-aws-services.sh
│   └── install-dependencies.sh
└── volume/                # LocalStack data persistence
```

### Manual Commands

```bash
# Install dependencies only
./scripts/install-dependencies.sh

# Start SAM local API only
sam local start-api --port 3000

# Build the SAM application
sam build

# Validate the SAM template
sam validate
```

### Environment Variables

Create a `.env` file to customize settings:

```bash
ENVIRONMENT=development
PROJECT_NAME=gameflex
AWS_REGION=us-east-1
```

## Troubleshooting

### Common Issues

1. **Port conflicts**: Make sure port 3000 is available
2. **Docker not running**: Ensure Docker is started before running the script
3. **Permission denied**: Make sure scripts are executable with `chmod +x *.sh scripts/*.sh`
4. **SAM CLI not found**: Install AWS SAM CLI following the official guide

### Logs

```bash
# View SAM local logs
# Logs are displayed in the terminal where start.sh is running
```

## Production Deployment

To deploy to AWS:

1. **Configure AWS credentials**
   ```bash
   aws configure
   ```

2. **Deploy with SAM**
   ```bash
   sam build
   sam deploy --guided
   ```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test locally with LocalStack
5. Submit a pull request

## License

MIT License - see LICENSE file for details

#!/bin/bash

# Build script for TypeScript Lambda functions
set -e

echo "🔨 Building TypeScript Lambda functions..."

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKEND_DIR="$(dirname "$SCRIPT_DIR")"

cd "$BACKEND_DIR"

# Clean previous build
echo "🧹 Cleaning previous build..."
rm -rf dist

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Build TypeScript
echo "🔧 Compiling TypeScript..."
npx tsc

# Copy package.json to dist for Lambda deployment
echo "📋 Copying package.json to dist..."
cp package.json dist/

# Install production dependencies in dist
echo "📦 Installing production dependencies in dist..."
cd dist
npm install --production --silent

# Remove unnecessary files from dist
echo "🧹 Cleaning up dist directory..."
rm -f package-lock.json
rm -rf node_modules/@types
rm -rf node_modules/typescript

echo "✅ Build completed successfully!"
echo "📁 Built files are in: $BACKEND_DIR/dist"

import {
    CognitoIdentityProviderClient,
    AdminC<PERSON><PERSON>serCommand,
    AdminSetUserPasswordCommand,
    Initiate<PERSON>uthCommand,
    GetTokensFromRefreshTokenCommand,
    GetUserCommand,
    AuthFlowType,
    MessageActionType,
    DeliveryMediumType
} from '@aws-sdk/client-cognito-identity-provider';
import {
    DynamoDBClient
} from '@aws-sdk/client-dynamodb';
import {
    DynamoDBDocumentClient,
    PutCommand,
    QueryCommand,
    GetCommand
} from '@aws-sdk/lib-dynamodb';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';

// Configure AWS SDK v3
const awsConfig = {
    region: process.env.AWS_REGION || 'us-west-2'
};

const cognitoClient = new CognitoIdentityProviderClient(awsConfig);
const dynamoClient = new DynamoDBClient(awsConfig);
const dynamodb = DynamoDBDocumentClient.from(dynamoClient);

// Environment variables
const USER_POOL_ID = process.env.USER_POOL_ID || process.env.USERPOOL_USER_POOL_ID;
const USER_POOL_CLIENT_ID = process.env.USER_POOL_CLIENT_ID || process.env.USERPOOLCLIENT_USER_POOL_CLIENT_ID;
const USERS_TABLE = process.env.USERS_TABLE || process.env.USERSTABLE_TABLE_NAME;

console.log('Environment variables:', { USER_POOL_ID, USER_POOL_CLIENT_ID, USERS_TABLE });

// Types
interface SignUpRequest {
    email: string;
    password: string;
    username: string;
    firstName?: string;
    lastName?: string;
}

interface SignInRequest {
    email: string;
    password: string;
}

interface RefreshTokenRequest {
    refreshToken: string;
}

interface User {
    id: string;
    email: string;
    username: string;
    firstName?: string;
    lastName?: string;
    cognitoUserId: string;
    createdAt: string;
    updatedAt: string;
}

interface TokenResponse {
    accessToken: string;
    idToken: string;
    refreshToken?: string;
}

// Helper function to create response
const createResponse = (statusCode: number, body: any): APIGatewayProxyResult => {
    return {
        statusCode,
        headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
        },
        body: JSON.stringify(body)
    };
};

// Generate unique ID
const generateId = (): string => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// Sign up function
const signUp = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { email, password, username, firstName, lastName }: SignUpRequest = JSON.parse(event.body);

        if (!email || !password || !username) {
            return createResponse(400, { error: 'Email, password, and username are required' });
        }

        // Create user in Cognito
        const createUserCommand = new AdminCreateUserCommand({
            UserPoolId: USER_POOL_ID,
            Username: email,
            TemporaryPassword: password,
            MessageAction: MessageActionType.SUPPRESS,
            UserAttributes: [
                { Name: 'email', Value: email },
                { Name: 'email_verified', Value: 'true' },
                { Name: 'given_name', Value: firstName || '' },
                { Name: 'family_name', Value: lastName || '' }
            ]
        });

        const cognitoUser = await cognitoClient.send(createUserCommand);
        console.log('Cognito user created:', cognitoUser.User?.Username);

        // Set permanent password
        const setPasswordCommand = new AdminSetUserPasswordCommand({
            UserPoolId: USER_POOL_ID,
            Username: email,
            Password: password,
            Permanent: true
        });

        await cognitoClient.send(setPasswordCommand);
        console.log('Password set for user:', email);

        // Create user record in DynamoDB
        const userId = generateId();
        const now = new Date().toISOString();

        const user: User = {
            id: userId,
            email,
            username,
            firstName: firstName || '',
            lastName: lastName || '',
            cognitoUserId: cognitoUser.User?.Username || email,
            createdAt: now,
            updatedAt: now
        };

        const putCommand = new PutCommand({
            TableName: USERS_TABLE,
            Item: user,
            ConditionExpression: 'attribute_not_exists(id) AND attribute_not_exists(email) AND attribute_not_exists(username)'
        });

        await dynamodb.send(putCommand);
        console.log('User created in DynamoDB:', userId);

        return createResponse(201, {
            message: 'User created successfully',
            user: {
                id: user.id,
                email: user.email,
                username: user.username,
                firstName: user.firstName,
                lastName: user.lastName
            }
        });

    } catch (error: any) {
        console.error('SignUp error:', error);

        if (error.name === 'UsernameExistsException' || error.name === 'ConditionalCheckFailedException') {
            return createResponse(409, { error: 'User already exists' });
        }

        return createResponse(500, {
            error: 'Failed to create user',
            details: error.message
        });
    }
};

// Sign in function
const signIn = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        const { email, password }: SignInRequest = JSON.parse(event.body);

        if (!email || !password) {
            return createResponse(400, { error: 'Email and password are required' });
        }

        const authCommand = new InitiateAuthCommand({
            ClientId: USER_POOL_CLIENT_ID,
            AuthFlow: AuthFlowType.USER_PASSWORD_AUTH,
            AuthParameters: {
                USERNAME: email,
                PASSWORD: password
            }
        });

        console.log('Attempting signin with USER_PASSWORD_AUTH flow');
        const authResult = await cognitoClient.send(authCommand);

        // Get user details from DynamoDB
        const queryCommand = new QueryCommand({
            TableName: USERS_TABLE,
            IndexName: 'EmailIndex',
            KeyConditionExpression: 'email = :email',
            ExpressionAttributeValues: {
                ':email': email
            }
        });

        const userResult = await dynamodb.send(queryCommand);

        if (!userResult.Items || userResult.Items.length === 0) {
            return createResponse(404, { error: 'User not found' });
        }

        const user = userResult.Items[0] as User;

        const tokens: TokenResponse = {
            accessToken: authResult.AuthenticationResult?.AccessToken || '',
            idToken: authResult.AuthenticationResult?.IdToken || '',
            refreshToken: authResult.AuthenticationResult?.RefreshToken
        };

        return createResponse(200, {
            message: 'Sign in successful',
            tokens,
            user: {
                id: user.id,
                email: user.email,
                username: user.username,
                firstName: user.firstName,
                lastName: user.lastName
            }
        });

    } catch (error: any) {
        console.error('SignIn error:', error);
        return createResponse(401, {
            error: 'Authentication failed',
            details: error.message
        });
    }
};

// Enhanced refresh token function using AWS SDK v3
const refreshToken = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        if (!event.body) {
            return createResponse(400, { error: 'Request body is required' });
        }

        console.log('Raw event.body:', event.body);
        const { refreshToken }: RefreshTokenRequest = JSON.parse(event.body);
        console.log('Provided refresh token (first 10 chars):', refreshToken?.slice(0, 10) + '...');

        if (!refreshToken) {
            return createResponse(400, { error: 'Refresh token is required' });
        }

        const refreshCommand = new GetTokensFromRefreshTokenCommand({
            ClientId: USER_POOL_CLIENT_ID,
            RefreshToken: refreshToken
        });

        console.log('GetTokensFromRefreshToken params:', {
            ClientId: USER_POOL_CLIENT_ID,
            RefreshToken: refreshToken ? refreshToken.slice(0, 10) + '...' : 'undefined'
        });

        const authResult = await cognitoClient.send(refreshCommand);
        console.log('AuthResult:', {
            AccessToken: !!authResult.AuthenticationResult?.AccessToken,
            IdToken: !!authResult.AuthenticationResult?.IdToken,
            RefreshToken: !!authResult.AuthenticationResult?.RefreshToken
        });

        const tokens: TokenResponse = {
            accessToken: authResult.AuthenticationResult?.AccessToken || '',
            idToken: authResult.AuthenticationResult?.IdToken || '',
            ...(authResult.AuthenticationResult?.RefreshToken && {
                refreshToken: authResult.AuthenticationResult.RefreshToken
            })
        };

        return createResponse(200, {
            message: 'Token refreshed successfully',
            tokens
        });

    } catch (error: any) {
        console.error('RefreshToken error:', {
            message: error.message,
            code: error.code || error.name,
            name: error.name,
            stack: error.stack?.split('\n')[0]
        });
        return createResponse(401, {
            error: 'Token refresh failed',
            details: error.message
        });
    }
};

// Validate token function
const validateToken = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        // Get token from Authorization header
        const authHeader = event.headers?.Authorization || event.headers?.authorization;

        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return createResponse(401, { error: 'Authorization header with Bearer token required' });
        }

        const accessToken = authHeader.substring(7);

        try {
            // Validate token with Cognito
            const getUserCommand = new GetUserCommand({
                AccessToken: accessToken
            });

            const cognitoUser = await cognitoClient.send(getUserCommand);

            // Get the user ID from Cognito (this is the 'sub' claim in the JWT)
            const userId = cognitoUser.Username;

            if (!userId) {
                return createResponse(401, { error: 'Invalid token: no user ID found' });
            }

            // Get user details from DynamoDB using user ID
            const getCommand = new GetCommand({
                TableName: USERS_TABLE,
                Key: {
                    id: userId
                }
            });

            const userResult = await dynamodb.send(getCommand);

            if (!userResult.Item) {
                return createResponse(404, { error: 'User not found' });
            }

            const user = userResult.Item as User;

            return createResponse(200, {
                message: 'Token is valid',
                valid: true,
                user: {
                    id: user.id,
                    email: user.email,
                    username: user.username,
                    firstName: user.firstName,
                    lastName: user.lastName
                }
            });

        } catch (cognitoError: any) {
            console.error('Token validation error:', cognitoError);
            return createResponse(401, {
                error: 'Invalid token',
                details: cognitoError.message
            });
        }

    } catch (error: any) {
        console.error('ValidateToken error:', error);
        return createResponse(500, {
            error: 'Internal server error',
            details: error.message
        });
    }
};

// Main handler
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path } = event;

    // Handle CORS preflight requests
    if (httpMethod === 'OPTIONS') {
        return createResponse(200, {});
    }

    try {
        switch (`${httpMethod} ${path}`) {
            case 'POST /auth/signup':
                return await signUp(event);
            case 'POST /auth/signin':
                return await signIn(event);
            case 'POST /auth/refresh':
                return await refreshToken(event);
            case 'GET /auth/validate':
                return await validateToken(event);
            default:
                return createResponse(404, { error: 'Not found' });
        }
    } catch (error: any) {
        console.error('Handler error:', error);
        return createResponse(500, {
            error: 'Internal server error',
            details: error.message
        });
    }
};

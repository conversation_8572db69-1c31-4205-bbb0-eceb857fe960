const AWS = require('aws-sdk');

// Configure AWS SDK
const awsConfig = {
    region: process.env.AWS_REGION || 'us-west-2'
};

if (process.env.AWS_SAM_LOCAL) {
    console.log('Running in SAM Local mode');
}

const cognito = new AWS.CognitoIdentityServiceProvider(awsConfig);
const dynamodb = new AWS.DynamoDB.DocumentClient(awsConfig);

const USER_POOL_ID = process.env.USER_POOL_ID || process.env.USERPOOL_USER_POOL_ID;
const USER_POOL_CLIENT_ID = process.env.USER_POOL_CLIENT_ID || process.env.USERPOOLCLIENT_USER_POOL_CLIENT_ID;
const USERS_TABLE = process.env.USERS_TABLE || process.env.USERSTABLE_TABLE_NAME;

console.log('Environment variables:', { USER_POOL_ID, USER_POOL_CLIENT_ID, USERS_TABLE });

const createResponse = (statusCode, body) => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});

// Sign up function
const signUp = async (event) => {
    try {
        const { email, password, username, firstName, lastName } = JSON.parse(event.body);

        if (!email || !password || !username) {
            return createResponse(400, { error: 'Email, password, and username are required' });
        }

        // Create user in Cognito
        const cognitoParams = {
            UserPoolId: USER_POOL_ID,
            Username: email,
            TemporaryPassword: password,
            MessageAction: 'SUPPRESS',
            UserAttributes: [
                { Name: 'email', Value: email },
                { Name: 'email_verified', Value: 'true' },
                { Name: 'given_name', Value: firstName || '' },
                { Name: 'family_name', Value: lastName || '' }
            ]
        };

        const cognitoUser = await cognito.adminCreateUser(cognitoParams).promise();

        // Set permanent password
        await cognito.adminSetUserPassword({
            UserPoolId: USER_POOL_ID,
            Username: email,
            Password: password,
            Permanent: true
        }).promise();

        // Get the user details to extract the sub (user ID)
        const userDetails = await cognito.adminGetUser({
            UserPoolId: USER_POOL_ID,
            Username: email
        }).promise();

        // Extract the sub attribute which will be our user ID
        const userAttributes = {};
        userDetails.UserAttributes.forEach(attr => {
            userAttributes[attr.Name] = attr.Value;
        });

        const userId = userAttributes.sub;
        const userRecord = {
            id: userId,
            email,
            username,
            firstName: firstName || '',
            lastName: lastName || '',
            cognito_user_id: cognitoUser.User.Username,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };

        await dynamodb.put({
            TableName: USERS_TABLE,
            Item: userRecord
        }).promise();

        return createResponse(201, {
            message: 'User created successfully',
            user: {
                id: userId,
                email,
                username,
                firstName: firstName || '',
                lastName: lastName || ''
            }
        });

    } catch (error) {
        console.error('SignUp error:', error);
        return createResponse(500, { error: 'Failed to create user', details: error.message });
    }
};

// Sign in function
const signIn = async (event) => {
    try {
        const { email, password } = JSON.parse(event.body);

        if (!email || !password) {
            return createResponse(400, { error: 'Email and password are required' });
        }

        const authParams = {
            ClientId: USER_POOL_CLIENT_ID,
            AuthFlow: 'USER_PASSWORD_AUTH',
            AuthParameters: {
                USERNAME: email,
                PASSWORD: password
            }
        };

        console.log('Attempting signin with USER_PASSWORD_AUTH flow');
        const authResult = await cognito.initiateAuth(authParams).promise();

        // Get user details from DynamoDB
        const userResult = await dynamodb.query({
            TableName: USERS_TABLE,
            IndexName: 'EmailIndex',
            KeyConditionExpression: 'email = :email',
            ExpressionAttributeValues: {
                ':email': email
            }
        }).promise();

        if (userResult.Items.length === 0) {
            return createResponse(404, { error: 'User not found' });
        }

        const user = userResult.Items[0];

        return createResponse(200, {
            message: 'Sign in successful',
            tokens: {
                accessToken: authResult.AuthenticationResult.AccessToken,
                refreshToken: authResult.AuthenticationResult.RefreshToken,
                idToken: authResult.AuthenticationResult.IdToken
            },
            user: {
                id: user.id,
                email: user.email,
                username: user.username,
                firstName: user.firstName,
                lastName: user.lastName
            }
        });

    } catch (error) {
        console.error('SignIn error:', error);
        return createResponse(401, { error: 'Authentication failed', details: error.message });
    }
};

// Enhanced refresh token function
const refreshToken = async (event) => {
    try {
        console.log('Raw event.body:', event.body);
        const { refreshToken } = JSON.parse(event.body);
        console.log('Provided refresh token (first 10 chars):', refreshToken?.slice(0, 10) + '...');

        if (!refreshToken) {
            return createResponse(400, { error: 'Refresh token is required' });
        }

        const refreshParams = {
            ClientId: USER_POOL_CLIENT_ID,
            AuthFlow: 'REFRESH_TOKEN_AUTH',
            AuthParameters: {
                REFRESH_TOKEN: refreshToken
            }
        };

        console.log('InitiateAuth params:', {
            ClientId: USER_POOL_CLIENT_ID,
            AuthFlow: 'REFRESH_TOKEN_AUTH'
        });

        const authResult = await cognito.initiateAuth(refreshParams).promise();
        console.log('AuthResult:', {
            AccessToken: !!authResult.AuthenticationResult?.AccessToken,
            IdToken: !!authResult.AuthenticationResult?.IdToken,
            RefreshToken: !!authResult.AuthenticationResult?.RefreshToken
        });

        return createResponse(200, {
            message: 'Token refreshed successfully',
            tokens: {
                accessToken: authResult.AuthenticationResult.AccessToken,
                idToken: authResult.AuthenticationResult.IdToken,
                ...(authResult.AuthenticationResult.RefreshToken && { refreshToken: authResult.AuthenticationResult.RefreshToken })
            }
        });

    } catch (error) {
        console.error('RefreshToken error:', {
            message: error.message,
            code: error.code,
            name: error.name,
            stack: error.stack?.split('\n')[0]
        });
        return createResponse(401, { error: 'Token refresh failed', details: error.message });
    }
};

// Validate token function
const validateToken = async (event) => {
    try {
        // Get token from Authorization header
        const authHeader = event.headers?.Authorization || event.headers?.authorization;

        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return createResponse(401, { error: 'Authorization header with Bearer token required' });
        }

        const accessToken = authHeader.substring(7);

        try {
            // Validate token with Cognito
            const getUserParams = {
                AccessToken: accessToken
            };

            const cognitoUser = await cognito.getUser(getUserParams).promise();

            // Get the user ID from Cognito (this is the 'sub' claim in the JWT)
            const userId = cognitoUser.Username;

            // Get user details from DynamoDB using user ID
            const userResult = await dynamodb.get({
                TableName: USERS_TABLE,
                Key: {
                    id: userId
                }
            }).promise();

            if (!userResult.Item) {
                return createResponse(404, { error: 'User not found' });
            }

            const user = userResult.Item;

            return createResponse(200, {
                message: 'Token is valid',
                valid: true,
                user: {
                    id: user.id,
                    email: user.email,
                    username: user.username,
                    firstName: user.firstName,
                    lastName: user.lastName
                }
            });

        } catch (cognitoError) {
            console.error('Token validation error:', cognitoError);
            return createResponse(401, {
                error: 'Invalid or expired token',
                valid: false
            });
        }

    } catch (error) {
        console.error('Validate token error:', error);
        return createResponse(500, { error: 'Internal server error', details: error.message });
    }
};

// Main handler
exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path } = event;

    // Handle CORS preflight requests
    if (httpMethod === 'OPTIONS') {
        return createResponse(200, {});
    }

    try {
        switch (`${httpMethod} ${path}`) {
            case 'POST /auth/signup':
                return await signUp(event);
            case 'POST /auth/signin':
                return await signIn(event);
            case 'POST /auth/refresh':
                return await refreshToken(event);
            case 'GET /auth/validate':
                return await validateToken(event);
            default:
                return createResponse(404, { error: 'Not found' });
        }
    } catch (error) {
        console.error('Handler error:', error);
        return createResponse(500, { error: 'Internal server error', details: error.message });
    }
};
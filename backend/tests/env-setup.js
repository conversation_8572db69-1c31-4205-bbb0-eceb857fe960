/**
 * Environment setup for tests
 * Loads environment variables from environment-specific .env file
 */

const path = require('path');
const dotenv = require('dotenv');

/**
 * Load environment variables from samconfig.toml or fallback to .env files
 * Priority: samconfig.toml > .env.{ENVIRONMENT} > .env
 */
function loadEnvironmentConfig() {
    const environment = process.env.ENVIRONMENT || 'development';
    const backendDir = path.resolve(__dirname, '..');

    // Try to load from samconfig.toml first
    try {
        const { execSync } = require('child_process');
        const scriptPath = path.join(backendDir, 'scripts', 'load-sam-env.sh');

        if (require('fs').existsSync(scriptPath)) {
            // Execute the shell script to load environment variables
            const result = execSync(`bash "${scriptPath}" "${environment}"`, {
                cwd: backendDir,
                encoding: 'utf8',
                stdio: 'pipe'
            });

            // Parse the output to extract environment variables
            const lines = result.split('\n');
            for (const line of lines) {
                if (line.includes('=') && !line.startsWith('#')) {
                    const [key, ...valueParts] = line.split('=');
                    const value = valueParts.join('=');
                    if (key && value) {
                        process.env[key.trim()] = value.trim();
                    }
                }
            }
            console.log(`✅ Loaded environment config from samconfig.toml [${environment}]`);
            return;
        }
    } catch (error) {
        console.warn(`⚠️  Failed to load from samconfig.toml:`, error.message);
    }

    // Fallback to .env files
    const envSpecificPath = path.join(backendDir, `.env.${environment}`);
    const defaultEnvPath = path.join(backendDir, '.env');

    let configLoaded = false;

    // Try environment-specific file
    try {
        if (require('fs').existsSync(envSpecificPath)) {
            dotenv.config({ path: envSpecificPath });
            console.log(`✅ Loaded environment config from .env.${environment} (fallback)`);
            configLoaded = true;
        }
    } catch (error) {
        console.warn(`⚠️  Failed to load .env.${environment}:`, error.message);
    }

    // Fallback to default .env file
    if (!configLoaded) {
        try {
            if (require('fs').existsSync(defaultEnvPath)) {
                dotenv.config({ path: defaultEnvPath });
                console.log(`✅ Loaded environment config from .env (fallback)`);
            }
        } catch (error) {
            console.warn(`⚠️  Failed to load .env:`, error.message);
        }
    }
}

// Load environment configuration
loadEnvironmentConfig();

// Override specific settings for test environment
process.env.NODE_ENV = 'test';
// Always set ENVIRONMENT to 'test' for unit tests
process.env.ENVIRONMENT = 'test';

// Disable AWS SDK retries for faster tests
process.env.AWS_MAX_ATTEMPTS = '1';

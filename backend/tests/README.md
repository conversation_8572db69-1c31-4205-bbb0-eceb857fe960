# GameFlex Backend Tests

This directory contains the test suite for the GameFlex Backend, providing comprehensive testing for all Lambda functions and API endpoints deployed via Terraform.

## Test Structure

```
tests/
├── unit/                   # Unit tests for individual Lambda functions
│   ├── auth.test.js       # Authentication service tests
│   ├── posts.test.js      # Posts service tests
│   ├── media.test.js      # Media service tests
│   ├── users.test.js      # Users service tests
│   └── health.test.js     # Health check tests
├── integration/           # Integration tests
│   └── api-integration.test.js  # End-to-end API tests
├── utils/                 # Test utilities and helpers
│   ├── test-data.js      # Test data generators
│   └── aws-mocks.js      # AWS service mocks
├── env-setup.js          # Environment setup for tests
├── setup.js              # Jest setup configuration
├── global-setup.js       # Global test setup
└── global-teardown.js    # Global test cleanup
```

## Prerequisites

Before running tests, ensure you have:

1. **Deployed Infrastructure**: Tests run against the deployed Terraform infrastructure
   ```bash
   # Deploy development environment first
   ./deploy.sh
   ```

2. **Environment Variables**: Tests use environment variables from the deployed infrastructure
   - Table names are automatically detected from Terraform outputs
   - AWS credentials should be configured

3. **Test Data**: Optionally seed the database with test data
   ```bash
   ./seed.sh
   ```

## Running Tests

### Quick Start
```bash
# Run all tests
npm test

# Run with coverage
npm run test:coverage

# Run in watch mode
npm run test:watch
```

### Specific Test Suites
```bash
# Unit tests only
npm run test:unit

# Integration tests only
npm run test:integration

# End-to-end tests only
npm run test:e2e
```

### Using the Test Runner Script
```bash
# Interactive test runner
./scripts/run-tests.sh

# Specific test suite
./scripts/run-tests.sh unit
./scripts/run-tests.sh integration
./scripts/run-tests.sh coverage
```

### Individual Test Files
```bash
# Run specific test file
npx jest tests/unit/auth.test.js

# Run specific test with watch mode
npx jest tests/unit/posts.test.js --watch

# Run specific test with coverage
npx jest tests/unit/health.test.js --coverage
```

## Test Categories

### Unit Tests
- **Purpose**: Test individual Lambda functions in isolation
- **Scope**: Single function behavior, input validation, error handling
- **Mocking**: AWS services are mocked using `aws-mocks.js`
- **Speed**: Fast execution (< 1 second per test)

### Integration Tests
- **Purpose**: Test multiple services working together
- **Scope**: API workflows, service interactions, data flow
- **Mocking**: Minimal mocking, focuses on service integration
- **Speed**: Moderate execution (1-5 seconds per test)

## Test Utilities

### TestDataGenerator
Provides consistent test data creation:
```javascript
const { TestDataGenerator } = require('./utils/test-data');

// Create test user
const user = TestDataGenerator.createUser();

// Create test post
const post = TestDataGenerator.createPost(user.id);

// Create API Gateway event
const event = TestDataGenerator.createAPIGatewayEvent({
  httpMethod: 'POST',
  path: '/posts',
  body: JSON.stringify(postData)
});
```

### AWS Mocks
Simplified AWS service mocking:
```javascript
const { mockDynamoDBGet, mockS3GetSignedUrl } = require('./utils/aws-mocks');

// Mock successful DynamoDB get
mockDynamoDBGet(testUser);

// Mock S3 signed URL generation
mockS3GetSignedUrl('https://test-url.com');
```

## Test Coverage

The test suite aims for:
- **Unit Tests**: 90%+ code coverage
- **Integration Tests**: 80%+ workflow coverage
- **Error Handling**: 100% error path coverage

### Coverage Reports
```bash
# Generate coverage report
npm run test:coverage

# View HTML coverage report
open coverage/lcov-report/index.html
```

## Writing New Tests

### Unit Test Template
```javascript
const { TestDataGenerator } = require('../utils/test-data');
const { resetAllMocks, mockDynamoDBGet } = require('../utils/aws-mocks');
const { handler } = require('../../src/your-service/index');

describe('Your Service Lambda Handler', () => {
  beforeEach(() => {
    resetAllMocks();
  });

  describe('GET /your-endpoint', () => {
    it('should handle request successfully', async () => {
      // Arrange
      mockDynamoDBGet(testData);
      const event = TestDataGenerator.createAPIGatewayEvent({
        httpMethod: 'GET',
        path: '/your-endpoint'
      });

      // Act
      const result = await handler(event, context);

      // Assert
      expect(result.statusCode).toBe(200);
    });
  });
});
```

### Integration Test Template
```javascript
describe('Service Integration', () => {
  it('should complete workflow successfully', async () => {
    // Step 1: Setup
    // Step 2: Execute workflow
    // Step 3: Verify results
  });
});
```

## Best Practices

1. **Test Isolation**: Each test should be independent
2. **Mock External Services**: Use AWS mocks for consistent testing
3. **Descriptive Names**: Test names should clearly describe the scenario
4. **Arrange-Act-Assert**: Follow the AAA pattern
5. **Error Testing**: Test both success and failure scenarios
6. **CORS Testing**: Verify CORS headers in API responses

## Debugging Tests

### Common Issues
1. **Mock not reset**: Use `resetAllMocks()` in `beforeEach`
2. **Environment variables**: Check `env-setup.js` for required vars
3. **Async/await**: Ensure proper async handling in tests

### Debug Commands
```bash
# Run single test with debug output
npx jest tests/unit/auth.test.js --verbose

# Run with Node.js debugger
node --inspect-brk node_modules/.bin/jest tests/unit/auth.test.js

# Enable console logs in tests
# Uncomment console.log mock in tests/setup.js
```

## Continuous Integration

Tests are designed to run in CI/CD environments:
- No external dependencies
- Consistent test data
- Proper cleanup
- Fast execution

### CI Configuration Example
```yaml
test:
  script:
    - npm install
    - npm run test:coverage
  coverage: '/Lines\s*:\s*(\d+\.\d+)%/'
```

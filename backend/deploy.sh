#!/bin/bash

# GameFlex Backend - Development Deployment Script
# This script deploys the Terraform infrastructure to the development environment

set -e  # Exit on any error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print functions
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🚀 Starting GameFlex Backend Development Deployment..."

# Set environment variables
export ENVIRONMENT=development
export PROJECT_NAME=gameflex

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Check if we're in the backend directory
if [ ! -f "$SCRIPT_DIR/terraform/main.tf" ]; then
    print_error "This script must be run from the backend directory"
    echo "   Current directory: $SCRIPT_DIR"
    echo "   Expected to find: terraform/main.tf"
    exit 1
fi

# Check if AWS credentials are configured
print_status "Checking AWS credentials..."
if ! aws sts get-caller-identity > /dev/null 2>&1; then
    print_error "AWS credentials not configured or invalid"
    echo "   Please run 'aws configure' or set AWS environment variables"
    exit 1
fi

print_success "Prerequisites check passed"

# Check if this is first time setup
FIRST_TIME_SETUP=false
if [ ! -d "$SCRIPT_DIR/terraform/.terraform" ]; then
    FIRST_TIME_SETUP=true
    print_warning "First time setup detected"
    echo "   This will initialize Terraform backend and create state bucket if needed"
fi

# Run the main deployment script
print_status "Running Terraform deployment for development..."

if [ "$FIRST_TIME_SETUP" = true ]; then
    # First time setup - initialize backend
    "$SCRIPT_DIR/terraform/scripts/setup-backend.sh" development
    "$SCRIPT_DIR/terraform/scripts/deploy.sh" development --init
else
    # Regular deployment
    "$SCRIPT_DIR/terraform/scripts/deploy.sh" development
fi

if [ $? -ne 0 ]; then
    print_error "Terraform deployment failed"
    exit 1
fi

print_success "Terraform deployment completed successfully!"

# Get the outputs
print_status "Deployment Information:"
echo "=========================="

cd "$SCRIPT_DIR/terraform"

# Get Terraform outputs
API_URL=$(terraform output -raw api_gateway_url 2>/dev/null || echo "Not available")
USER_POOL_ID=$(terraform output -raw user_pool_id 2>/dev/null || echo "Not available")
USER_POOL_CLIENT_ID=$(terraform output -raw user_pool_client_id 2>/dev/null || echo "Not available")
R2_SECRET_NAME=$(terraform output -raw r2_secret_name 2>/dev/null || echo "Not available")

echo "API Gateway URL: $API_URL"
echo "User Pool ID: $USER_POOL_ID"
echo "User Pool Client ID: $USER_POOL_CLIENT_ID"
echo "R2 Secret Name: $R2_SECRET_NAME"
echo ""

# Instructions for next steps
echo "🔧 Next Steps:"
echo "=============="
echo "1. Seed the database:"
echo "   ./seed.sh"
echo ""
echo "2. Test the deployment:"
echo "   curl $API_URL/health"
echo ""
echo "3. Start local development:"
echo "   ./start.sh"
echo ""
echo "🎉 Development deployment complete!"

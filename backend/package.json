{"name": "gameflex-terraform-backend", "version": "1.0.0", "description": "GameFlex Backend using Terraform for infrastructure deployment and management", "main": "terraform/main.tf", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "build:clean": "rm -rf dist && npm run build", "start": "./start.sh", "stop": "./stop.sh", "install-deps": "./scripts/install-dependencies.sh", "deploy": "./deploy.sh", "deploy:staging": "./deploy-staging.sh", "deploy:production": "./deploy-production.sh", "seed": "./seed.sh", "seed:staging": "./seed.sh staging", "seed:production": "./seed.sh production", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest tests/unit", "test:integration": "jest tests/integration", "test:e2e": "jest tests/e2e", "test:run": "./scripts/run-tests.sh", "terraform:init": "cd terraform && terraform init", "terraform:plan": "cd terraform && terraform plan", "terraform:apply": "cd terraform && terraform apply", "terraform:destroy": "cd terraform && terraform destroy", "terraform:validate": "cd terraform && terraform validate", "terraform:fmt": "cd terraform && terraform fmt"}, "keywords": ["aws", "terraform", "infrastructure-as-code", "serverless", "lambda", "api-gateway", "dynamodb", "cognito", "gameflex"], "author": "GameFlex Team", "license": "MIT", "devDependencies": {"@babel/core": "^7.23.0", "@babel/preset-env": "^7.23.0", "@types/aws-lambda": "^8.10.150", "@types/jest": "^29.5.12", "@types/node": "^20.19.7", "axios": "^1.6.0", "babel-jest": "^29.7.0", "dotenv": "^17.2.0", "jest": "^29.7.0", "typescript": "^5.3.0"}, "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.490.0", "@aws-sdk/client-dynamodb": "^3.490.0", "@aws-sdk/client-secrets-manager": "^3.490.0", "@aws-sdk/lib-dynamodb": "^3.490.0", "aws-sdk": "^2.1499.0", "uuid": "^9.0.1"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/gameflex/gameflex-backend"}, "bugs": {"url": "https://github.com/gameflex/gameflex-backend/issues"}, "homepage": "https://github.com/gameflex/gameflex-backend#readme"}
#!/bin/bash

# GameFlex Data Seeding Script
# This script runs the comprehensive AWS data seeding for the Terraform backend
#
# Usage: ./seed.sh [environment] [-f|--force]
#   environment    Environment to seed (development, staging, production) - defaults to development
#   -f, --force    Delete all existing data before seeding (fresh start)

set -e

# Parse command line arguments
ENVIRONMENT="development"  # Default to development
FORCE_FLAG=""

while [[ $# -gt 0 ]]; do
    case $1 in
        development|staging|production)
            ENVIRONMENT="$1"
            shift
            ;;
        -f|--force)
            FORCE_FLAG="-f"
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [environment] [-f|--force]"
            echo "  environment    Environment to seed (development, staging, production) - defaults to development"
            echo "  -f, --force    Delete all existing data before seeding (fresh start)"
            echo "  -h, --help     Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use -h or --help for usage information"
            exit 1
            ;;
    esac
done

if [ -n "$FORCE_FLAG" ]; then
    echo "🌱 GameFlex Data Seeding (FORCE MODE - Fresh Data)"
else
    echo "🌱 GameFlex Data Seeding"
fi
echo "Environment: $ENVIRONMENT"
echo "========================"
echo

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Check if we're in the backend directory
if [ ! -f "$SCRIPT_DIR/terraform/main.tf" ]; then
    echo "❌ Error: This script must be run from the backend directory"
    echo "   Current directory: $SCRIPT_DIR"
    echo "   Expected to find: terraform/main.tf"
    exit 1
fi

# Check if AWS credentials are configured
echo "🔍 Checking AWS credentials..."
if ! aws sts get-caller-identity > /dev/null 2>&1; then
    echo "❌ Error: AWS credentials not configured"
    echo "   Please configure your AWS credentials:"
    echo "   aws configure"
    echo "   Or set environment variables:"
    echo "   export AWS_ACCESS_KEY_ID=your_key"
    echo "   export AWS_SECRET_ACCESS_KEY=your_secret"
    echo "   export AWS_DEFAULT_REGION=us-west-2"
    exit 1
fi

# Check if Terraform infrastructure is deployed
echo "🔍 Checking if Terraform infrastructure is deployed..."
cd "$SCRIPT_DIR/terraform"

# Check if terraform state exists
if [ ! -f ".terraform/terraform.tfstate" ] && [ ! -f "terraform.tfstate" ]; then
    echo "⚠️  Warning: Terraform state not found locally"
    echo "   Make sure Terraform infrastructure is deployed first:"
    echo "   ./deploy.sh (for development)"
    echo "   ./deploy-staging.sh (for staging)"
    echo "   ./deploy-production.sh (for production)"
    echo
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Aborted."
        exit 1
    fi
fi

# Get table names from Terraform outputs
echo "🔍 Getting resource names from Terraform outputs..."

USERS_TABLE=$(terraform output -raw users_table_name 2>/dev/null || echo "")
POSTS_TABLE=$(terraform output -raw posts_table_name 2>/dev/null || echo "")
MEDIA_TABLE=$(terraform output -raw media_table_name 2>/dev/null || echo "")
USER_PROFILES_TABLE=$(terraform output -raw user_profiles_table_name 2>/dev/null || echo "")
COMMENTS_TABLE=$(terraform output -raw comments_table_name 2>/dev/null || echo "")
LIKES_TABLE=$(terraform output -raw likes_table_name 2>/dev/null || echo "")
FOLLOWS_TABLE=$(terraform output -raw follows_table_name 2>/dev/null || echo "")
CHANNELS_TABLE=$(terraform output -raw channels_table_name 2>/dev/null || echo "")
CHANNEL_MEMBERS_TABLE=$(terraform output -raw channel_members_table_name 2>/dev/null || echo "")
REFLEXES_TABLE=$(terraform output -raw reflexes_table_name 2>/dev/null || echo "")

# Fallback to expected names if outputs are not available
if [ -z "$USERS_TABLE" ]; then
    USERS_TABLE="gameflex-${ENVIRONMENT}-Users"
    echo "⚠️  Using fallback table name: $USERS_TABLE"
fi

# Check if main table exists
if ! aws dynamodb describe-table --table-name "$USERS_TABLE" > /dev/null 2>&1; then
    echo "⚠️  Warning: Terraform infrastructure doesn't seem to be deployed"
    echo "   Table not found: $USERS_TABLE"
    echo "   Make sure to deploy your Terraform infrastructure first:"
    echo "   ./deploy.sh (for development)"
    echo "   ./deploy-staging.sh (for staging)"
    echo "   ./deploy-production.sh (for production)"
    echo
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Aborted."
        exit 1
    fi
fi

echo "🚀 Starting data seeding process..."
echo

# Set environment variables for the seeding script
export ENVIRONMENT=${ENVIRONMENT}
export PROJECT_NAME=${PROJECT_NAME:-gameflex}
export AWS_REGION=${AWS_REGION:-us-west-2}

# Set table names from Terraform outputs
export USERS_TABLE=${USERS_TABLE}
export POSTS_TABLE=${POSTS_TABLE:-"gameflex-${ENVIRONMENT}-Posts"}
export MEDIA_TABLE=${MEDIA_TABLE:-"gameflex-${ENVIRONMENT}-Media"}
export USER_PROFILES_TABLE=${USER_PROFILES_TABLE:-"gameflex-${ENVIRONMENT}-UserProfiles"}
export COMMENTS_TABLE=${COMMENTS_TABLE:-"gameflex-${ENVIRONMENT}-Comments"}
export LIKES_TABLE=${LIKES_TABLE:-"gameflex-${ENVIRONMENT}-Likes"}
export FOLLOWS_TABLE=${FOLLOWS_TABLE:-"gameflex-${ENVIRONMENT}-Follows"}
export CHANNELS_TABLE=${CHANNELS_TABLE:-"gameflex-${ENVIRONMENT}-Channels"}
export CHANNEL_MEMBERS_TABLE=${CHANNEL_MEMBERS_TABLE:-"gameflex-${ENVIRONMENT}-ChannelMembers"}
export REFLEXES_TABLE=${REFLEXES_TABLE:-"gameflex-${ENVIRONMENT}-Reflexes"}

# Go back to backend directory
cd "$SCRIPT_DIR"

# Run the existing seeding script
"$SCRIPT_DIR/scripts/seed-data.sh" $FORCE_FLAG

echo
echo "✅ Data seeding completed!"
echo
echo "📋 Next steps:"
echo "   1. Test the API endpoints using the Terraform-deployed infrastructure"
echo "   2. Check the seeded data in your Flutter app"
echo
echo "🔑 Test user credentials:"
echo "   📧 <EMAIL> / DevPassword123!"
echo "   👑 <EMAIL> / AdminPassword123!"
echo "   👤 <EMAIL> / JohnPassword123!"
echo "   👤 <EMAIL> / JanePassword123!"
echo "   👤 <EMAIL> / MikePassword123!"
echo

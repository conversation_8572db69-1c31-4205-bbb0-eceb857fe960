#!/usr/bin/env node

const axios = require('axios');

// Configuration
const API_URL = 'https://imubdkcdu7.execute-api.us-west-2.amazonaws.com/v1';

async function testRefreshToken() {
  try {
    console.log('🔄 TESTING REFRESH TOKEN FUNCTIONALITY');
    console.log('API URL:', API_URL);
    console.log('='.repeat(50));

    const timestamp = Date.now();
    const email = `test-refresh-${timestamp}@example.com`;
    const username = `testuser${timestamp}`;

    console.log('1. Creating new user...');
    const signupResponse = await axios.post(`${API_URL}/auth/signup`, {
      email: email,
      password: 'TestPassword123!',
      username: username,
      firstName: 'Test',
      lastName: 'User'
    });
    console.log('✅ User created successfully:', signupResponse.status);

    console.log('\n2. Signing in to get tokens...');
    const signinResponse = await axios.post(`${API_URL}/auth/signin`, {
      email: email,
      password: 'TestPassword123!'
    });
    console.log('✅ Signin successful:', signinResponse.status);

    console.log('\n2. Waiting 10 seconds...');
    await new Promise(resolve => setTimeout(resolve, 10000));

    const { accessToken, refreshToken, idToken } = signinResponse.data.tokens;
    console.log('📋 Token info:');
    console.log('  - Access token:', accessToken);
    console.log('  - Refresh token:', refreshToken);
    console.log('  - ID token:', idToken);

    console.log('\n2a. Validating token immediately after sign-in...');
    const validateResponse1 = await axios.get(`${API_URL}/auth/validate`, {
      headers: { Authorization: `Bearer ${accessToken}` }
    });
    console.log('✅ Initial token validation successful:', validateResponse1.status);
    console.log('👤 User data:', {
      userId: validateResponse1.data.user.id,
      email: validateResponse1.data.user.email,
      username: validateResponse1.data.user.username
    });

    console.log('\n2b. Waiting 10 seconds...');
    await new Promise(resolve => setTimeout(resolve, 10000));

    console.log('\n2c. Validating token after 10 seconds...');
    const validateResponse2 = await axios.get(`${API_URL}/auth/validate`, {
      headers: { Authorization: `Bearer ${accessToken}` }
    });
    console.log('✅ Token validation after 10 seconds successful:', validateResponse2.status);
    console.log('👤 User data still valid:', {
      userId: validateResponse2.data.user.id,
      email: validateResponse2.data.user.email,
      username: validateResponse2.data.user.username
    });

    console.log('\n3. Testing refresh token...');
    const refreshResponse = await axios.post(`${API_URL}/auth/refresh`, {
      refreshToken: refreshToken
    });

    console.log('🎉 SUCCESS! Refresh token worked!');
    console.log('✅ Status:', refreshResponse.status);

    const newTokens = refreshResponse.data.tokens;
    console.log('📋 New token info:');
    console.log('  - New access token length:', newTokens.accessToken.length);
    console.log('  - New ID token length:', newTokens.idToken.length);
    console.log('  - New refresh token:', newTokens.refreshToken ? `${newTokens.refreshToken.length} chars` : 'Not provided');

    console.log('\n4. Testing new access token with validate endpoint...');
    const validateResponse3 = await axios.get(`${API_URL}/auth/validate`, {
      headers: { Authorization: `Bearer ${newTokens.accessToken}` }
    });
    console.log('✅ New access token validation successful:', validateResponse3.status);
    console.log('👤 User data with new token:', {
      userId: validateResponse3.data.user.id,
      email: validateResponse3.data.user.email,
      username: validateResponse3.data.user.username
    });

    console.log('\n5. Testing new access token with profile endpoint...');
    const profileResponse = await axios.get(`${API_URL}/users/profile`, {
      headers: { Authorization: `Bearer ${newTokens.accessToken}` }
    });

    console.log('✅ New access token works perfectly!', profileResponse.status);
    console.log('👤 Profile data:', {
      userId: profileResponse.data.userId,
      email: profileResponse.data.email,
      username: profileResponse.data.username
    });

    console.log('\n' + '='.repeat(50));
    console.log('🚀🚀🚀 REFRESH TOKEN FUNCTIONALITY IS FULLY OPERATIONAL! 🚀🚀🚀');
    console.log('🎯 ALL BACKEND AUTHENTICATION FEATURES WORKING! 🎯');

  } catch (error) {
    console.log('\n❌ ERROR OCCURRED:');
    console.log('Status:', error.response?.status);
    console.log('Error data:', error.response?.data);

    if (error.response?.status === 500) {
      console.log('\n💡 This might be a server error. Check CloudWatch logs with:');
      console.log('   aws logs tail /aws/lambda/gameflex-staging-auth --since 2m');
    } else if (error.response?.status === 401) {
      console.log('\n💡 This is likely the refresh token issue we\'re debugging.');
      console.log('   Check the Lambda function logs for detailed error info.');
    }

    console.log('\n🔍 For debugging, check:');
    console.log('1. CloudWatch logs: aws logs tail /aws/lambda/gameflex-staging-auth --since 2m');
    console.log('2. Cognito User Pool configuration');
    console.log('3. Lambda function environment variables');

    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testRefreshToken();
}

module.exports = testRefreshToken;

# GameFlex Backend Troubleshooting Guide

This guide helps resolve common issues with the GameFlex Terraform backend setup and deployment.

## Quick Diagnostics

### 1. Check Terraform State
```bash
cd terraform
terraform show
```

### 2. Validate Terraform Configuration
```bash
cd terraform
terraform validate
```

### 3. Check AWS Connection
```bash
aws sts get-caller-identity
```

### 4. Test Deployed API
```bash
# Get API URL from Terraform outputs
cd terraform
API_URL=$(terraform output -raw api_gateway_url)
curl $API_URL/health
```

## Common Issues

### ❌ "Backend Not Initialized"

**Cause**: Terraform backend not set up

**Solution**:
```bash
# Setup backend for your environment
./terraform/scripts/setup-backend.sh development
./terraform/scripts/setup-backend.sh staging
```

### ❌ "State Lock Error"

**Cause**: Previous Terraform operation was interrupted

**Solution**:
```bash
cd terraform
terraform force-unlock <lock-id>
```

### ❌ "Resource Already Exists"

**Cause**: Trying to create resources that already exist

**Solution**:
```bash
# Option 1: Import existing resources
terraform import aws_dynamodb_table.users gameflex-development-Users

# Option 2: Destroy and recreate (development only)
terraform destroy -var-file="environments/development.tfvars"
```

### ❌ "Failed to load environment configuration"

**Cause**: Missing or incorrect .tfvars files

**Solution**:
```bash
# 1. Check if .tfvars files exist
ls -la terraform/environments/

# 2. Create from examples if missing
cp .env.example .env
cp .env.staging.example .env.staging

# 3. Edit with your actual credentials
nano .env
nano .env.staging
```

**Required variables in .env**:
- `R2_ACCOUNT_ID`
- `R2_ACCESS_KEY_ID` 
- `R2_SECRET_ACCESS_KEY`
- `R2_ENDPOINT`
- `R2_BUCKET_NAME`
- `R2_PUBLIC_URL`
- `CLOUDFLARE_API_TOKEN`

### ❌ "Cannot connect to AWS"

**Cause**: AWS credentials not configured or incorrect region

**Solution**:
```bash
# 1. Check AWS configuration
aws configure list

# 2. Configure AWS credentials
aws configure
# Enter your AWS Access Key ID, Secret, and region (us-west-2)

# 3. Test connection
aws sts get-caller-identity

# 4. Alternative: Use environment variables
export AWS_ACCESS_KEY_ID=your_key
export AWS_SECRET_ACCESS_KEY=your_secret  
export AWS_DEFAULT_REGION=us-west-2
```

### ❌ "R2 secret does not exist"

**Cause**: SAM stack not deployed yet

**Solution**:
```bash
# 1. Deploy SAM stack first
sam build
sam deploy

# 2. Then run seeding
./scripts/seed-data.sh
```

### ❌ "samconfig.toml not found"

**Cause**: Missing samconfig.toml file

**Solution**:
```bash
# 1. Copy from example
cp samconfig.toml.example samconfig.toml

# 2. No need to edit - secrets are now in .env files
```

### ❌ "Failed to update secret"

**Cause**: Insufficient AWS permissions or wrong region

**Solution**:
```bash
# 1. Check your AWS permissions
aws iam get-user

# 2. Ensure you have SecretsManager permissions
# Your AWS user/role needs:
# - secretsmanager:GetSecretValue
# - secretsmanager:PutSecretValue
# - secretsmanager:DescribeSecret

# 3. Check region matches
echo $AWS_REGION  # Should be us-west-2
```

## Environment-Specific Issues

### Development Environment

**Issue**: Local development not working
```bash
# 1. Ensure .env file exists with secrets
ls -la .env

# 2. Test environment loading
./test-env-loading.sh development

# 3. Deploy and seed
sam build && sam deploy
./scripts/seed-data.sh
```

### Staging Environment

**Issue**: Staging deployment fails
```bash
# 1. Ensure .env.staging exists
ls -la .env.staging

# 2. Set certificate ARN
export CERTIFICATE_ARN="arn:aws:acm:us-west-2:account:certificate/cert-id"

# 3. Deploy staging
./deploy-staging.sh

# 4. Seed staging data
./seed-staging.sh
```

## File Structure Check

Your backend directory should have:
```
backend/
├── .env                    # Development secrets (gitignored)
├── .env.staging           # Staging secrets (gitignored)  
├── .env.example           # Development template (committed)
├── .env.staging.example   # Staging template (committed)
├── samconfig.toml         # Deployment config (gitignored)
├── samconfig.toml.example # Config template (committed)
├── scripts/
│   ├── seed-data.sh       # Main seeding script
│   └── load-sam-env.sh    # Environment loader
├── test-env-loading.sh    # Environment test script
├── debug-aws.sh          # AWS debug script
└── seed-staging.sh       # Staging seeder
```

## Step-by-Step Setup

### First Time Setup
```bash
# 1. Copy configuration templates
cp samconfig.toml.example samconfig.toml
cp .env.example .env
cp .env.staging.example .env.staging

# 2. Edit .env files with your actual credentials
nano .env        # Add your R2 and CloudFlare credentials
nano .env.staging # Add your staging credentials

# 3. Configure AWS
aws configure    # Set region to us-west-2

# 4. Test configuration
./test-env-loading.sh
./debug-aws.sh

# 5. Deploy and seed
sam build && sam deploy
./scripts/seed-data.sh
```

### Staging Setup
```bash
# 1. Ensure staging environment configured
./test-env-loading.sh staging

# 2. Set certificate ARN for custom domain
export CERTIFICATE_ARN="your-certificate-arn"

# 3. Deploy staging
./deploy-staging.sh

# 4. Seed staging data
./seed-staging.sh
```

## Getting Help

If you're still having issues:

1. **Run diagnostics**:
   ```bash
   ./test-env-loading.sh
   ./debug-aws.sh
   ```

2. **Check logs**: Look for specific error messages in the output

3. **Verify files**: Ensure all required files exist and have correct content

4. **AWS permissions**: Verify your AWS user has the necessary permissions

5. **Region**: Ensure you're using us-west-2 region consistently

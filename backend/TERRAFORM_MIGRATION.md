# GameFlex Backend - SAM to Terraform Migration Guide

This guide walks you through migrating from AWS SAM to Terraform for the GameFlex backend infrastructure.

## Overview

The migration replaces the SAM-based infrastructure with Terraform while maintaining all existing functionality:

- ✅ All DynamoDB tables with same names and configurations
- ✅ Cognito User Pool with same settings
- ✅ Lambda functions with same code and environment variables
- ✅ API Gateway with same routes and authorizers
- ✅ Secrets Manager for R2 and application configuration
- ✅ Environment-specific deployments (development, staging, production)
- ✅ Data seeding scripts
- ✅ Automated deployment scripts

## Migration Steps

### Step 1: Backup Current Data (Recommended)

Before starting the migration, backup your current data:

```bash
# Export DynamoDB tables (optional, for safety)
aws dynamodb scan --table-name gameflex-development-Users --output json > users-backup.json
aws dynamodb scan --table-name gameflex-development-Posts --output json > posts-backup.json
# ... repeat for other tables if needed
```

### Step 2: Set Up Terraform Backend

Create the S3 bucket and DynamoDB table for Terraform state management:

```bash
# Setup backend for development
./terraform/scripts/setup-backend.sh development

# Setup backend for staging (if you have staging environment)
./terraform/scripts/setup-backend.sh staging
```

### Step 3: Configure Environment Variables

Update the Terraform environment configuration files with your current values:

```bash
# Copy values from your current .env file to terraform/environments/development.tfvars
cp .env .env.backup  # Backup current .env

# Edit terraform/environments/development.tfvars with your R2 credentials
# The values should match what's currently in your .env file
```

### Step 4: Deploy Terraform Infrastructure

#### Option A: Fresh Deployment (Recommended for Development)

Deploy new infrastructure with Terraform:

```bash
# Deploy development environment
./deploy-terraform-development.sh
```

This creates new resources with the same names. If you get conflicts, you may need to delete the existing SAM stack first:

```bash
# Delete existing SAM stack (development only!)
aws cloudformation delete-stack --stack-name gameflex-development
aws cloudformation wait stack-delete-complete --stack-name gameflex-development
```

#### Option B: Import Existing Resources (Advanced)

If you want to keep existing resources and import them into Terraform:

```bash
# Initialize Terraform
cd terraform
terraform init -backend-config="backend-configs/development.hcl"

# Import existing resources (example for Users table)
terraform import aws_dynamodb_table.users gameflex-development-Users

# Import Cognito User Pool
terraform import aws_cognito_user_pool.main us-west-2_jZQ65BOBt

# Import User Pool Client
terraform import aws_cognito_user_pool_client.main us-west-2_jZQ65BOBt/4vk16jkh0cnh4r2ebg2ltfvbfg

# ... repeat for other resources
```

### Step 5: Verify Deployment

Check that all resources are created correctly:

```bash
# Get Terraform outputs
cd terraform
terraform output

# Test the API
curl $(terraform output -raw api_gateway_url)/health
```

### Step 6: Seed Data

Seed the database with test data:

```bash
# Seed development data
./seed-terraform-data.sh development
```

### Step 7: Update Application Configuration

If your application references any hardcoded resource names or ARNs, update them to use the new Terraform-deployed resources.

### Step 8: Test Everything

Run your test suite to ensure everything works:

```bash
# Run integration tests
npm run test:integration

# Test specific endpoints
curl $(terraform output -raw api_gateway_url)/auth/validate
```

### Step 9: Clean Up SAM Resources (Optional)

Once you've verified everything works with Terraform, you can clean up the old SAM resources:

```bash
# Delete SAM stack (be careful!)
aws cloudformation delete-stack --stack-name gameflex-development

# Remove SAM build artifacts
rm -rf .aws-sam/
```

## Key Differences

### File Structure

**SAM (Old)**:
```
backend/
├── template.yaml
├── samconfig-development.toml
├── deploy-staging.sh
└── src/
```

**Terraform (New)**:
```
backend/
├── terraform/
│   ├── main.tf
│   ├── environments/development.tfvars
│   └── scripts/deploy.sh
├── deploy-terraform-development.sh
└── src/
```

### Deployment Commands

**SAM (Old)**:
```bash
sam build && sam deploy --config-file samconfig-development.toml
```

**Terraform (New)**:
```bash
./deploy-terraform-development.sh
# or
npm run deploy:terraform:dev
```

### Environment Configuration

**SAM (Old)**:
- Configuration in `samconfig-development.toml`
- Parameters passed via command line

**Terraform (New)**:
- Configuration in `terraform/environments/development.tfvars`
- Type-safe variable definitions
- Environment-specific backend configurations

### State Management

**SAM (Old)**:
- CloudFormation manages state
- Stack-based deployments

**Terraform (New)**:
- Terraform state in S3 with DynamoDB locking
- Resource-based deployments
- Better drift detection

## Troubleshooting

### Resource Already Exists Errors

If you get "resource already exists" errors:

1. **Option 1**: Delete the existing SAM stack first
2. **Option 2**: Import the existing resources into Terraform
3. **Option 3**: Use different resource names in Terraform

### Lambda Function Code Updates

Terraform automatically detects changes in Lambda function source code and redeploys functions when needed.

### API Gateway Changes

The API Gateway configuration is now more explicit in Terraform, with separate resources for each route and method.

### Environment Variables

Lambda environment variables are now managed through Terraform variables and are consistent across all functions.

## Rollback Plan

If you need to rollback to SAM:

1. **Keep SAM files**: Don't delete `template.yaml` and `samconfig-*.toml` files until migration is complete
2. **Backup Terraform state**: The Terraform state is stored in S3
3. **Redeploy SAM**: Use the original SAM deployment commands
4. **Restore data**: Use the data backups created in Step 1

## Benefits of Terraform

- **Better Resource Management**: More granular control over resources
- **Type Safety**: Variable validation and type checking
- **Modularity**: Reusable modules and better organization
- **State Management**: Better state tracking and drift detection
- **Multi-Environment**: Easier management of multiple environments
- **Community**: Large ecosystem and community support

## Next Steps

After successful migration:

1. **Update Documentation**: Update any deployment documentation
2. **Train Team**: Ensure team members understand Terraform workflows
3. **Set Up CI/CD**: Integrate Terraform with your CI/CD pipeline
4. **Monitor**: Set up monitoring for the new infrastructure
5. **Optimize**: Review and optimize resource configurations

## Support

If you encounter issues during migration:

1. Check the Terraform logs for detailed error messages
2. Verify AWS credentials and permissions
3. Ensure all required variables are set in the `.tfvars` files
4. Check the Terraform state for any inconsistencies
5. Refer to the main README.md for troubleshooting tips
